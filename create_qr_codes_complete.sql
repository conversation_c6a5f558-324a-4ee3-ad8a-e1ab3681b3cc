-- <PERSON><PERSON><PERSON> hoàn chỉnh tạo QR codes tuân thủ tất cả constraints
-- Chạy script này trong Supabase SQL Editor

-- Ki<PERSON>m tra tenant và lấy thông tin
DO $$
DECLARE
    tenant_count INTEGER;
    active_tenant_id UUID;
    tenant_name TEXT;
BEGIN
    SELECT COUNT(*), MIN(id), MIN(name) 
    INTO tenant_count, active_tenant_id, tenant_name
    FROM tenants WHERE is_active = true;
    
    IF tenant_count = 0 THEN
        RAISE EXCEPTION 'No active tenants found. Please create a tenant first.';
    END IF;
    
    RAISE NOTICE '✅ Found % active tenant(s)', tenant_count;
    RAISE NOTICE '🏢 Using tenant: % (ID: %)', tenant_name, active_tenant_id;
END $$;

-- 1. Tạo reception points (tuân thủ UNIQUE constraint)
INSERT INTO tenant_message_reception_points (
    tenant_id, name, code, description, icon_url, is_active, priority, view_order, created_at, updated_at
) 
SELECT t.id, rp.name, rp.code, rp.description, rp.icon_url, true, rp.priority, rp.priority, now(), now()
FROM tenants t
CROSS JOIN (
    VALUES 
        ('Guest Services', 'guest_services', 'Main guest services desk', '/icons/guest-services.svg', 1),
        ('Food & Beverage', 'food_beverage', 'Restaurant and room service', '/icons/restaurant.svg', 2),
        ('Spa & Wellness', 'spa_wellness', 'Spa and wellness center', '/icons/spa.svg', 3),
        ('Housekeeping', 'housekeeping', 'Room cleaning and maintenance', '/icons/housekeeping.svg', 4),
        ('Concierge', 'concierge', 'Concierge and local information', '/icons/concierge.svg', 5)
) AS rp(name, code, description, icon_url, priority)
WHERE t.is_active = true
ON CONFLICT (tenant_id, code) DO NOTHING;

-- 2. Tạo QR code types (tuân thủ UNIQUE constraint)
INSERT INTO tenant_qr_code_types (
    tenant_id, name, description, default_action, icon_url, color_code, is_active, created_at, updated_at
)
SELECT t.id, qt.name, qt.description, qt.default_action, qt.icon_url, qt.color_code, true, now(), now()
FROM tenants t
CROSS JOIN (
    VALUES 
        ('Chat Support', 'General chat support for guests', 'chat', '/icons/chat.svg', '#4F46E5'),
        ('Information Display', 'Display information to guests', 'info', '/icons/info.svg', '#059669'),
        ('Service Request', 'Allow guests to request services', 'service', '/icons/service.svg', '#DC2626'),
        ('Feedback Collection', 'Collect guest feedback', 'feedback', '/icons/feedback.svg', '#7C2D12')
) AS qt(name, description, default_action, icon_url, color_code)
WHERE t.is_active = true
ON CONFLICT (tenant_id, name) DO NOTHING;

-- 3. Tạo rooms (để đảm bảo foreign key constraint)
INSERT INTO tenant_rooms (
    tenant_id, room_number, room_type, floor, status, room_category, description, is_active, 
    reception_point_id, created_at, updated_at
)
SELECT 
    t.id, r.room_number, r.room_type, r.floor, 'available', r.room_category, r.description, true,
    (SELECT id FROM tenant_message_reception_points WHERE tenant_id = t.id AND code = 'guest_services' LIMIT 1),
    now(), now()
FROM tenants t
CROSS JOIN (
    VALUES 
        ('102', 'Executive Suite', '1', 'Deluxe', 'Executive Suite with city view and premium amenities'),
        ('201', 'Standard Room', '2', 'Standard', 'Comfortable standard room with garden view'),
        ('301', 'VIP Suite', '3', 'VIP', 'Luxury VIP suite with panoramic views')
) AS r(room_number, room_type, floor, room_category, description)
WHERE t.is_active = true
ON CONFLICT (tenant_id, room_number) DO NOTHING;

-- 4. Tạo QR Codes tuân thủ tất cả constraints
WITH tenant_info AS (
    SELECT id as tenant_id FROM tenants WHERE is_active = true LIMIT 1
),
qr_data AS (
    SELECT 
        ti.tenant_id,
        qr.code_value,
        qr.name,
        qr.location,
        qr.description,
        qr.room_number,
        qr.target_type,
        qr.target_department,
        qr.custom_action
    FROM tenant_info ti
    CROSS JOIN (
        VALUES 
            (
                'room102-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
                'Room 102 Guest Services',
                'Room 102 - Executive Suite',
                'QR Code for Room 102 guest services and support',
                '102',
                'room',
                'guest_services',
                '{"action": "chat", "welcome_message": "Welcome to Room 102! How can we assist you?", "room_number": "102"}'
            ),
            (
                'restaurant-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
                'Restaurant Services',
                'Main Restaurant - Ground Floor',
                'QR Code for restaurant orders and services',
                NULL,
                'general',
                'food_beverage',
                '{"action": "service", "service_type": "restaurant", "welcome_message": "Welcome to our restaurant! What would you like to order?"}'
            ),
            (
                'spa-wellness-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
                'Spa & Wellness Center',
                'Spa Center - 2nd Floor',
                'QR Code for spa bookings and wellness services',
                NULL,
                'general',
                'spa_wellness',
                '{"action": "service", "service_type": "spa", "welcome_message": "Welcome to our Spa! How can we help you relax today?"}'
            ),
            (
                'concierge-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
                'Concierge Services',
                'Main Lobby - Concierge Desk',
                'QR Code for concierge and local information services',
                NULL,
                'general',
                'concierge',
                '{"action": "info", "content": "Welcome! Our concierge team is here to help with local attractions, transportation, and recommendations."}'
            ),
            (
                'housekeeping-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
                'Housekeeping Services',
                'Service Floor - Housekeeping Department',
                'QR Code for room cleaning and maintenance requests',
                NULL,
                'general',
                'housekeeping',
                '{"action": "service", "service_type": "housekeeping", "welcome_message": "Need housekeeping assistance? Let us know how we can help!"}'
            )
    ) AS qr(code_value, name, location, description, room_number, target_type, target_department, custom_action)
)
INSERT INTO tenant_qr_codes (
    tenant_id, code_value, name, location, description, room_number, target_type, target_id,
    target_department, qr_type_id, reception_point_id, custom_action, is_active, status,
    qr_type, code_type, counts_against_limit, scan_count, created_at, updated_at
)
SELECT 
    qd.tenant_id,
    qd.code_value,
    qd.name,
    qd.location,
    qd.description,
    qd.room_number,
    qd.target_type,
    NULL, -- target_id is NULL for room and general types
    qd.target_department,
    (SELECT id FROM tenant_qr_code_types WHERE tenant_id = qd.tenant_id AND default_action = 
        CASE 
            WHEN qd.target_department IN ('guest_services', 'housekeeping') THEN 'service'
            WHEN qd.target_department = 'concierge' THEN 'info'
            ELSE 'service'
        END 
        LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE tenant_id = qd.tenant_id AND code = qd.target_department LIMIT 1),
    qd.custom_action::jsonb,
    true,
    'active',
    'TENANT',
    'TENANT',
    true,
    0,
    now(),
    now()
FROM qr_data qd
ON CONFLICT (tenant_id, code_value) DO NOTHING;

-- 5. Hiển thị kết quả
SELECT 
    qr.code_value,
    qr.name,
    qr.location,
    qr.room_number,
    qr.target_type,
    qr.target_department,
    qr.scan_count,
    rp.name as reception_point_name,
    qt.name as qr_type_name,
    qt.default_action,
    -- URLs test
    'http://localhost:3003/qr/' || qr.code_value || '?lang=en' as test_url_en,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=vi' as test_url_vi,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=fr' as test_url_fr,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=ko' as test_url_ko,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=ja' as test_url_ja
FROM tenant_qr_codes qr
LEFT JOIN tenant_message_reception_points rp ON qr.reception_point_id = rp.id
LEFT JOIN tenant_qr_code_types qt ON qr.qr_type_id = qt.id
WHERE qr.created_at > now() - interval '2 minutes'
  AND qr.is_active = true
ORDER BY qr.created_at DESC;

-- 6. Thông báo hoàn thành
DO $$
DECLARE
    qr_count INTEGER;
    tenant_name TEXT;
BEGIN
    SELECT COUNT(*) INTO qr_count 
    FROM tenant_qr_codes 
    WHERE created_at > now() - interval '2 minutes';
    
    SELECT name INTO tenant_name 
    FROM tenants 
    WHERE is_active = true 
    LIMIT 1;
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ Successfully created % QR codes for tenant: %', qr_count, COALESCE(tenant_name, 'Unknown');
    RAISE NOTICE '';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '1. Copy the code_value from the results above';
    RAISE NOTICE '2. Update test_multiple_guests.html with actual code_values';
    RAISE NOTICE '3. Open Staff Dashboard: http://localhost:3003/staff/dashboard';
    RAISE NOTICE '4. Open Guest URLs in separate tabs to test multiple sessions';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 Test URLs format: http://localhost:3003/qr/[code_value]?lang=[language]';
    RAISE NOTICE '🌐 Supported languages: en, vi, fr, ko, ja, zh, de, es, it, th';
END $$;
