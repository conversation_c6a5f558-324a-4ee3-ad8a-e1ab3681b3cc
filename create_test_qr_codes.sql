-- <PERSON>ript để tạo QR codes mới cho testing
-- Chạy script này trong Supabase SQL Editor

-- 1. Tạo QR Code cho Room 102
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    -- Thay YOUR_TENANT_ID bằng tenant_id thực tế (có thể lấy từ bảng tenants)
    (SELECT id FROM tenants LIMIT 1), -- Lấy tenant đầu tiên
    'room102-test-' || extract(epoch from now())::text, -- Code value unique
    'Room 102 Guest Services',
    'Room 102 - Deluxe Suite',
    'QR Code for Room 102 guest services and support',
    '102',
    'room',
    'guest_services',
    (SELECT id FROM tenant_message_reception_points WHERE name ILIKE '%guest%' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 2. Tạo QR Code cho Restaurant
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants LIMIT 1),
    'restaurant-test-' || extract(epoch from now())::text,
    'Restaurant Services',
    'Main Restaurant - Ground Floor',
    'QR Code for restaurant orders and services',
    null,
    'area',
    'food_beverage',
    (SELECT id FROM tenant_message_reception_points WHERE name ILIKE '%food%' OR name ILIKE '%restaurant%' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 3. Tạo QR Code cho Spa
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants LIMIT 1),
    'spa-wellness-' || extract(epoch from now())::text,
    'Spa & Wellness Center',
    'Spa Center - 2nd Floor',
    'QR Code for spa bookings and wellness services',
    null,
    'area',
    'spa_wellness',
    (SELECT id FROM tenant_message_reception_points WHERE name ILIKE '%spa%' OR name ILIKE '%wellness%' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 4. Hiển thị các QR codes vừa tạo
SELECT 
    id,
    code_value,
    name,
    location,
    room_number,
    target_type,
    target_department,
    created_at
FROM tenant_qr_codes 
WHERE created_at > now() - interval '1 minute'
ORDER BY created_at DESC;

-- 5. Tạo sẵn một số reception points nếu chưa có
INSERT INTO tenant_message_reception_points (
    tenant_id,
    name,
    code,
    description,
    is_active,
    priority
) VALUES 
    ((SELECT id FROM tenants LIMIT 1), 'Guest Services', 'guest_services', 'Main guest services desk', true, 1),
    ((SELECT id FROM tenants LIMIT 1), 'Food & Beverage', 'food_beverage', 'Restaurant and room service', true, 2),
    ((SELECT id FROM tenants LIMIT 1), 'Spa & Wellness', 'spa_wellness', 'Spa and wellness center', true, 3),
    ((SELECT id FROM tenants LIMIT 1), 'Housekeeping', 'housekeeping', 'Room cleaning and maintenance', true, 4),
    ((SELECT id FROM tenants LIMIT 1), 'Concierge', 'concierge', 'Concierge and local information', true, 5)
ON CONFLICT (tenant_id, code) DO NOTHING;
