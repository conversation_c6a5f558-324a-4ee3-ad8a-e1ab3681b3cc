-- <PERSON><PERSON>t để tạo QR codes mới cho testing multiple guests
-- Ch<PERSON>y script này trong Supabase SQL Editor

-- <PERSON><PERSON><PERSON> tra tenant hiện có
DO $$
DECLARE
    tenant_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO tenant_count FROM tenants WHERE is_active = true;
    IF tenant_count = 0 THEN
        RAISE EXCEPTION 'No active tenants found. Please create a tenant first.';
    END IF;
    RAISE NOTICE 'Found % active tenant(s)', tenant_count;
END $$;

-- Tạo reception points nếu chưa có
INSERT INTO tenant_message_reception_points (
    tenant_id,
    name,
    code,
    description,
    is_active,
    priority
)
SELECT
    t.id,
    rp.name,
    rp.code,
    rp.description,
    true,
    rp.priority
FROM tenants t
CROSS JOIN (
    VALUES
        ('Guest Services', 'guest_services', 'Main guest services desk', 1),
        ('Food & Beverage', 'food_beverage', 'Restaurant and room service', 2),
        ('Spa & Wellness', 'spa_wellness', 'Spa and wellness center', 3),
        ('Housekeeping', 'housekeeping', 'Room cleaning and maintenance', 4),
        ('Concierge', 'concierge', 'Concierge and local information', 5)
) AS rp(name, code, description, priority)
WHERE t.is_active = true
ON CONFLICT (tenant_id, code) DO NOTHING;

-- 1. Tạo QR Code cho Room 102
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'room102-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Room 102 Guest Services',
    'Room 102 - Deluxe Suite',
    'QR Code for Room 102 guest services and support',
    '102',
    'room',
    'guest_services',
    (SELECT id FROM tenant_message_reception_points WHERE code = 'guest_services' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 2. Tạo QR Code cho Restaurant
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'restaurant-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Restaurant Services',
    'Main Restaurant - Ground Floor',
    'QR Code for restaurant orders and services',
    null,
    'area',
    'food_beverage',
    (SELECT id FROM tenant_message_reception_points WHERE code = 'food_beverage' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 3. Tạo QR Code cho Spa
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'spa-wellness-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Spa & Wellness Center',
    'Spa Center - 2nd Floor',
    'QR Code for spa bookings and wellness services',
    null,
    'area',
    'spa_wellness',
    (SELECT id FROM tenant_message_reception_points WHERE code = 'spa_wellness' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 4. Tạo QR Code cho Concierge
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'concierge-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Concierge Services',
    'Main Lobby - Concierge Desk',
    'QR Code for concierge and local information services',
    null,
    'area',
    'concierge',
    (SELECT id FROM tenant_message_reception_points WHERE code = 'concierge' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 5. Tạo QR Code cho Housekeeping
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    reception_point_id,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'housekeeping-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Housekeeping Services',
    'Service Floor - Housekeeping Department',
    'QR Code for room cleaning and maintenance requests',
    null,
    'area',
    'housekeeping',
    (SELECT id FROM tenant_message_reception_points WHERE code = 'housekeeping' LIMIT 1),
    true,
    'active',
    now(),
    now(),
    0
);

-- 4. Hiển thị các QR codes vừa tạo
SELECT 
    id,
    code_value,
    name,
    location,
    room_number,
    target_type,
    target_department,
    created_at
FROM tenant_qr_codes 
WHERE created_at > now() - interval '1 minute'
ORDER BY created_at DESC;

-- 5. Tạo sẵn một số reception points nếu chưa có
INSERT INTO tenant_message_reception_points (
    tenant_id,
    name,
    code,
    description,
    is_active,
    priority
) VALUES 
    ((SELECT id FROM tenants LIMIT 1), 'Guest Services', 'guest_services', 'Main guest services desk', true, 1),
    ((SELECT id FROM tenants LIMIT 1), 'Food & Beverage', 'food_beverage', 'Restaurant and room service', true, 2),
    ((SELECT id FROM tenants LIMIT 1), 'Spa & Wellness', 'spa_wellness', 'Spa and wellness center', true, 3),
    ((SELECT id FROM tenants LIMIT 1), 'Housekeeping', 'housekeeping', 'Room cleaning and maintenance', true, 4),
    ((SELECT id FROM tenants LIMIT 1), 'Concierge', 'concierge', 'Concierge and local information', true, 5)
ON CONFLICT (tenant_id, code) DO NOTHING;
