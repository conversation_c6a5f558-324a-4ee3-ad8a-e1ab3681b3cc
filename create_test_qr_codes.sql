-- <PERSON><PERSON>t để tạo QR codes mới cho testing multiple guests
-- Chạy script này trong Supabase SQL Editor
-- <PERSON><PERSON> thủ tất cả Foreign Keys, Constraints, và Triggers

-- <PERSON><PERSON><PERSON> tra và lấy thông tin tenant
DO $$
DECLARE
    tenant_count INTEGER;
    active_tenant_id UUID;
    tenant_name TEXT;
BEGIN
    SELECT COUNT(*), MIN(id), MIN(name)
    INTO tenant_count, active_tenant_id, tenant_name
    FROM tenants WHERE is_active = true;

    IF tenant_count = 0 THEN
        RAISE EXCEPTION 'No active tenants found. Please create a tenant first.';
    END IF;

    RAISE NOTICE '✅ Found % active tenant(s)', tenant_count;
    RAISE NOTICE '🏢 Using tenant: % (ID: %)', tenant_name, active_tenant_id;
END $$;

-- 1. Tạo reception points nếu chưa có (tuân thủ UNIQUE constraint)
INSERT INTO tenant_message_reception_points (
    tenant_id,
    name,
    code,
    description,
    icon_url,
    is_active,
    priority,
    view_order,
    created_at,
    updated_at
)
SELECT
    t.id,
    rp.name,
    rp.code,
    rp.description,
    rp.icon_url,
    true,
    rp.priority,
    rp.priority,
    now(),
    now()
FROM tenants t
CROSS JOIN (
    VALUES
        ('Guest Services', 'guest_services', 'Main guest services desk', '/icons/guest-services.svg', 1),
        ('Food & Beverage', 'food_beverage', 'Restaurant and room service', '/icons/restaurant.svg', 2),
        ('Spa & Wellness', 'spa_wellness', 'Spa and wellness center', '/icons/spa.svg', 3),
        ('Housekeeping', 'housekeeping', 'Room cleaning and maintenance', '/icons/housekeeping.svg', 4),
        ('Concierge', 'concierge', 'Concierge and local information', '/icons/concierge.svg', 5)
) AS rp(name, code, description, icon_url, priority)
WHERE t.is_active = true
ON CONFLICT (tenant_id, code) DO NOTHING;

-- 2. Tạo QR code types nếu chưa có (tuân thủ UNIQUE constraint)
INSERT INTO tenant_qr_code_types (
    tenant_id,
    name,
    description,
    default_action,
    icon_url,
    color_code,
    is_active,
    created_at,
    updated_at
)
SELECT
    t.id,
    qt.name,
    qt.description,
    qt.default_action,
    qt.icon_url,
    qt.color_code,
    true,
    now(),
    now()
FROM tenants t
CROSS JOIN (
    VALUES
        ('Chat Support', 'General chat support for guests', 'chat', '/icons/chat.svg', '#4F46E5'),
        ('Information Display', 'Display information to guests', 'info', '/icons/info.svg', '#059669'),
        ('Service Request', 'Allow guests to request services', 'service', '/icons/service.svg', '#DC2626'),
        ('Feedback Collection', 'Collect guest feedback', 'feedback', '/icons/feedback.svg', '#7C2D12')
) AS qt(name, description, default_action, icon_url, color_code)
WHERE t.is_active = true
ON CONFLICT (tenant_id, name) DO NOTHING;

-- 3. Tạo rooms nếu chưa có (để đảm bảo foreign key constraint)
INSERT INTO tenant_rooms (
    tenant_id,
    room_number,
    room_type,
    floor,
    status,
    room_category,
    description,
    is_active,
    reception_point_id,
    created_at,
    updated_at
)
SELECT
    t.id,
    r.room_number,
    r.room_type,
    r.floor,
    'available',
    r.room_category,
    r.description,
    true,
    (SELECT id FROM tenant_message_reception_points WHERE tenant_id = t.id AND code = 'guest_services' LIMIT 1),
    now(),
    now()
FROM tenants t
CROSS JOIN (
    VALUES
        ('102', 'Executive Suite', '1', 'Deluxe', 'Executive Suite with city view and premium amenities'),
        ('201', 'Standard Room', '2', 'Standard', 'Comfortable standard room with garden view'),
        ('301', 'VIP Suite', '3', 'VIP', 'Luxury VIP suite with panoramic views')
) AS r(room_number, room_type, floor, room_category, description)
WHERE t.is_active = true
ON CONFLICT (tenant_id, room_number) DO NOTHING;

-- 4. Tạo QR Codes với tất cả ràng buộc được tuân thủ

-- 4.1. Tạo QR Code cho Room 102 (tuân thủ tất cả constraints)
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_id,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    qr_type,
    code_type,
    counts_against_limit,
    created_at,
    updated_at,
    scan_count
)
SELECT
    t.id,
    'room102-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Room 102 Guest Services',
    'Room 102 - Executive Suite',
    'QR Code for Room 102 guest services and support',
    '102',
    'room',
    NULL, -- target_id is NULL for room type (per check constraint)
    'guest_services',
    (SELECT id FROM tenant_qr_code_types WHERE tenant_id = t.id AND default_action = 'chat' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE tenant_id = t.id AND code = 'guest_services' LIMIT 1),
    '{"action": "chat", "welcome_message": "Welcome to Room 102! How can we assist you?", "room_number": "102"}',
    true,
    'active',
    'TENANT',
    'TENANT',
    true,
    now(),
    now(),
    0
FROM tenants t
WHERE t.is_active = true
LIMIT 1;

-- 4.2. Tạo QR Code cho Restaurant (area type)
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_id,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    qr_type,
    code_type,
    counts_against_limit,
    created_at,
    updated_at,
    scan_count
)
SELECT
    t.id,
    'restaurant-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Restaurant Services',
    'Main Restaurant - Ground Floor',
    'QR Code for restaurant orders and services',
    NULL, -- room_number is NULL for area type
    'area',
    NULL, -- Will be updated with actual area_id if needed
    'food_beverage',
    (SELECT id FROM tenant_qr_code_types WHERE tenant_id = t.id AND default_action = 'service' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE tenant_id = t.id AND code = 'food_beverage' LIMIT 1),
    '{"action": "service", "service_type": "restaurant", "welcome_message": "Welcome to our restaurant! What would you like to order?", "location": "main_restaurant"}',
    true,
    'active',
    'TENANT',
    'TENANT',
    true,
    now(),
    now(),
    0
FROM tenants t
WHERE t.is_active = true
LIMIT 1;

-- 3. Tạo QR Code cho Spa
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'spa-wellness-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Spa & Wellness Center',
    'Spa Center - 2nd Floor',
    'QR Code for spa bookings and wellness services',
    null,
    'area',
    'spa_wellness',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'service' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'spa_wellness' LIMIT 1),
    '{"action": "service", "service_type": "spa", "welcome_message": "Welcome to our Spa! How can we help you relax today?"}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 4. Tạo QR Code cho Concierge
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'concierge-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Concierge Services',
    'Main Lobby - Concierge Desk',
    'QR Code for concierge and local information services',
    null,
    'area',
    'concierge',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'info' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'concierge' LIMIT 1),
    '{"action": "info", "content": "Welcome! Our concierge team is here to help with local attractions, transportation, and recommendations."}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 5. Tạo QR Code cho Housekeeping
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'housekeeping-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Housekeeping Services',
    'Service Floor - Housekeeping Department',
    'QR Code for room cleaning and maintenance requests',
    null,
    'area',
    'housekeeping',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'service' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'housekeeping' LIMIT 1),
    '{"action": "service", "service_type": "housekeeping", "welcome_message": "Need housekeeping assistance? Let us know how we can help!"}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 6. Hiển thị các QR codes vừa tạo với URLs test
SELECT
    qr.id,
    qr.code_value,
    qr.name,
    qr.location,
    qr.room_number,
    qr.target_type,
    qr.target_department,
    qr.scan_count,
    qr.is_active,
    qr.created_at,
    -- Thông tin liên quan
    rp.name as reception_point_name,
    qt.name as qr_type_name,
    qt.default_action,
    -- Tạo URLs test cho từng QR code
    'http://localhost:3003/qr/' || qr.code_value || '?lang=en' as test_url_en,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=vi' as test_url_vi,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=fr' as test_url_fr,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=ko' as test_url_ko,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=ja' as test_url_ja
FROM tenant_qr_codes qr
LEFT JOIN tenant_message_reception_points rp ON qr.reception_point_id = rp.id
LEFT JOIN tenant_qr_code_types qt ON qr.qr_type_id = qt.id
WHERE qr.created_at > now() - interval '5 minutes'
  AND qr.is_active = true
ORDER BY qr.created_at DESC;

-- 7. Hiển thị thông tin reception points
SELECT
    id,
    name,
    code,
    description,
    is_active,
    priority,
    view_order
FROM tenant_message_reception_points
WHERE is_active = true
ORDER BY priority;

-- 8. Hiển thị thông tin QR code types
SELECT
    id,
    name,
    description,
    default_action,
    is_active
FROM tenant_qr_code_types
WHERE is_active = true
ORDER BY name;

-- 9. Thông báo hoàn thành
DO $$
DECLARE
    qr_count INTEGER;
    tenant_name TEXT;
BEGIN
    -- Đếm số QR codes vừa tạo
    SELECT COUNT(*) INTO qr_count
    FROM tenant_qr_codes
    WHERE created_at > now() - interval '5 minutes';

    -- Lấy tên tenant
    SELECT name INTO tenant_name
    FROM tenants
    WHERE is_active = true
    LIMIT 1;

    RAISE NOTICE '✅ Successfully created % QR codes for tenant: %', qr_count, COALESCE(tenant_name, 'Unknown');
    RAISE NOTICE '';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '1. Copy the code_value from the results above';
    RAISE NOTICE '2. Update test_multiple_guests.html with the actual code_values';
    RAISE NOTICE '3. Open Staff Dashboard: http://localhost:3003/staff/dashboard';
    RAISE NOTICE '4. Open Guest URLs in separate tabs to test multiple sessions';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 Test URLs format:';
    RAISE NOTICE '   http://localhost:3003/qr/[code_value]?lang=[language]';
    RAISE NOTICE '';
    RAISE NOTICE '🌐 Supported languages: en, vi, fr, ko, ja, zh, de, es, it, th';
END $$;
