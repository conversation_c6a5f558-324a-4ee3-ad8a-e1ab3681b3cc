-- <PERSON><PERSON><PERSON> để tạo QR codes mới cho testing multiple guests
-- <PERSON><PERSON>y script này trong Supabase SQL Editor

-- Ki<PERSON>m tra tenant hiện có
DO $$
DECLARE
    tenant_count INTEGER;
    active_tenant_id UUID;
BEGIN
    SELECT COUNT(*), MIN(id) INTO tenant_count, active_tenant_id
    FROM tenants WHERE is_active = true;

    IF tenant_count = 0 THEN
        RAISE EXCEPTION 'No active tenants found. Please create a tenant first.';
    END IF;

    RAISE NOTICE 'Found % active tenant(s). Using tenant ID: %', tenant_count, active_tenant_id;
END $$;

-- Tạo reception points nếu chưa có
INSERT INTO tenant_message_reception_points (
    tenant_id,
    name,
    code,
    description,
    is_active,
    priority,
    view_order,
    created_at,
    updated_at
)
SELECT
    t.id,
    rp.name,
    rp.code,
    rp.description,
    true,
    rp.priority,
    rp.priority,
    now(),
    now()
FROM tenants t
CROSS JOIN (
    VALUES
        ('Guest Services', 'guest_services', 'Main guest services desk', 1),
        ('Food & Beverage', 'food_beverage', 'Restaurant and room service', 2),
        ('Spa & Wellness', 'spa_wellness', 'Spa and wellness center', 3),
        ('Housekeeping', 'housekeeping', 'Room cleaning and maintenance', 4),
        ('Concierge', 'concierge', 'Concierge and local information', 5)
) AS rp(name, code, description, priority)
WHERE t.is_active = true
ON CONFLICT (tenant_id, code) DO NOTHING;

-- Tạo QR code types nếu chưa có
INSERT INTO tenant_qr_code_types (
    tenant_id,
    name,
    description,
    default_action,
    is_active,
    created_at,
    updated_at
)
SELECT
    t.id,
    qt.name,
    qt.description,
    qt.default_action,
    true,
    now(),
    now()
FROM tenants t
CROSS JOIN (
    VALUES
        ('Chat Support', 'General chat support for guests', 'chat'),
        ('Information Display', 'Display information to guests', 'info'),
        ('Service Request', 'Allow guests to request services', 'service'),
        ('Feedback Collection', 'Collect guest feedback', 'feedback')
) AS qt(name, description, default_action)
WHERE t.is_active = true
ON CONFLICT (tenant_id, name) DO NOTHING;

-- 1. Tạo QR Code cho Room 102
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'room102-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Room 102 Guest Services',
    'Room 102 - Deluxe Suite',
    'QR Code for Room 102 guest services and support',
    '102',
    'room',
    'guest_services',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'chat' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'guest_services' LIMIT 1),
    '{"action": "chat", "welcome_message": "Welcome to Room 102! How can we assist you?"}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 2. Tạo QR Code cho Restaurant
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'restaurant-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Restaurant Services',
    'Main Restaurant - Ground Floor',
    'QR Code for restaurant orders and services',
    null,
    'area',
    'food_beverage',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'service' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'food_beverage' LIMIT 1),
    '{"action": "service", "service_type": "room_service", "welcome_message": "Welcome to our restaurant! What would you like to order?"}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 3. Tạo QR Code cho Spa
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'spa-wellness-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Spa & Wellness Center',
    'Spa Center - 2nd Floor',
    'QR Code for spa bookings and wellness services',
    null,
    'area',
    'spa_wellness',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'service' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'spa_wellness' LIMIT 1),
    '{"action": "service", "service_type": "spa", "welcome_message": "Welcome to our Spa! How can we help you relax today?"}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 4. Tạo QR Code cho Concierge
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'concierge-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Concierge Services',
    'Main Lobby - Concierge Desk',
    'QR Code for concierge and local information services',
    null,
    'area',
    'concierge',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'info' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'concierge' LIMIT 1),
    '{"action": "info", "content": "Welcome! Our concierge team is here to help with local attractions, transportation, and recommendations."}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 5. Tạo QR Code cho Housekeeping
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    qr_type_id,
    reception_point_id,
    custom_action,
    is_active,
    status,
    created_at,
    updated_at,
    scan_count
) VALUES (
    (SELECT id FROM tenants WHERE is_active = true LIMIT 1),
    'housekeeping-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
    'Housekeeping Services',
    'Service Floor - Housekeeping Department',
    'QR Code for room cleaning and maintenance requests',
    null,
    'area',
    'housekeeping',
    (SELECT id FROM tenant_qr_code_types WHERE default_action = 'service' LIMIT 1),
    (SELECT id FROM tenant_message_reception_points WHERE code = 'housekeeping' LIMIT 1),
    '{"action": "service", "service_type": "housekeeping", "welcome_message": "Need housekeeping assistance? Let us know how we can help!"}',
    true,
    'active',
    now(),
    now(),
    0
);

-- 6. Hiển thị các QR codes vừa tạo với URLs test
SELECT
    qr.id,
    qr.code_value,
    qr.name,
    qr.location,
    qr.room_number,
    qr.target_type,
    qr.target_department,
    qr.scan_count,
    qr.is_active,
    qr.created_at,
    -- Thông tin liên quan
    rp.name as reception_point_name,
    qt.name as qr_type_name,
    qt.default_action,
    -- Tạo URLs test cho từng QR code
    'http://localhost:3003/qr/' || qr.code_value || '?lang=en' as test_url_en,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=vi' as test_url_vi,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=fr' as test_url_fr,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=ko' as test_url_ko,
    'http://localhost:3003/qr/' || qr.code_value || '?lang=ja' as test_url_ja
FROM tenant_qr_codes qr
LEFT JOIN tenant_message_reception_points rp ON qr.reception_point_id = rp.id
LEFT JOIN tenant_qr_code_types qt ON qr.qr_type_id = qt.id
WHERE qr.created_at > now() - interval '5 minutes'
  AND qr.is_active = true
ORDER BY qr.created_at DESC;

-- 7. Hiển thị thông tin reception points
SELECT
    id,
    name,
    code,
    description,
    is_active,
    priority,
    view_order
FROM tenant_message_reception_points
WHERE is_active = true
ORDER BY priority;

-- 8. Hiển thị thông tin QR code types
SELECT
    id,
    name,
    description,
    default_action,
    is_active
FROM tenant_qr_code_types
WHERE is_active = true
ORDER BY name;

-- 9. Thông báo hoàn thành
DO $$
DECLARE
    qr_count INTEGER;
    tenant_name TEXT;
BEGIN
    -- Đếm số QR codes vừa tạo
    SELECT COUNT(*) INTO qr_count
    FROM tenant_qr_codes
    WHERE created_at > now() - interval '5 minutes';

    -- Lấy tên tenant
    SELECT name INTO tenant_name
    FROM tenants
    WHERE is_active = true
    LIMIT 1;

    RAISE NOTICE '✅ Successfully created % QR codes for tenant: %', qr_count, COALESCE(tenant_name, 'Unknown');
    RAISE NOTICE '';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '1. Copy the code_value from the results above';
    RAISE NOTICE '2. Update test_multiple_guests.html with the actual code_values';
    RAISE NOTICE '3. Open Staff Dashboard: http://localhost:3003/staff/dashboard';
    RAISE NOTICE '4. Open Guest URLs in separate tabs to test multiple sessions';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 Test URLs format:';
    RAISE NOTICE '   http://localhost:3003/qr/[code_value]?lang=[language]';
    RAISE NOTICE '';
    RAISE NOTICE '🌐 Supported languages: en, vi, fr, ko, ja, zh, de, es, it, th';
END $$;
