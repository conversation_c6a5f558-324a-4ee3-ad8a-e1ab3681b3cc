.dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f8fafc;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #f97316;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  p {
    margin-top: 16px;
    color: #64748b;
    font-weight: 500;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Header Styles
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;

  .headerLeft {
    .logo h1 {
      color: #f97316;
      font-size: 24px;
      font-weight: 700;
      margin: 0;
    }
  }

  .headerCenter {
    .statusSelector {
      display: flex;
      align-items: center;
      gap: 8px;
      
      label {
        font-weight: 500;
        color: #374151;
      }
      
      .statusSelect {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 14px;
        background: white;
        cursor: pointer;
        
        &:focus {
          outline: none;
          border-color: #f97316;
          box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
      }
    }
  }

  .headerRight {
    display: flex;
    align-items: center;
    gap: 16px;

    .userInfo {
      display: flex;
      align-items: center;
      gap: 12px;

      .userAvatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f97316, #ea580c);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 16px;
      }

      .userDetails {
        display: flex;
        flex-direction: column;

        .userName {
          font-weight: 600;
          color: #111827;
          font-size: 14px;
        }

        .userRole {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }

    .logoutButton {
      padding: 8px 16px;
      background: #ef4444;
      color: white;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #dc2626;
        transform: translateY(-2px);
      }
    }
  }
}

// Main Content Styles
.mainContent {
  display: flex;
  flex: 1;
  overflow: hidden;
}

// Sidebar Styles
.sidebar {
  width: 380px;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;

  .sidebarHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;

    h2 {
      font-size: 18px;
      font-weight: 600;
      color: #111827;
      margin: 0;
    }

    .chatCount {
      background: #f97316;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }
  }

  .chatList {
    flex: 1;
    overflow-y: auto;
    padding: 8px;

    .chatItem {
      padding: 16px;
      margin-bottom: 8px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s;
      border: 1px solid transparent;

      &:hover {
        background: #f8fafc;
        border-color: #e2e8f0;
      }

      &.selected {
        background: #fef3e2;
        border-color: #f97316;
        
        .guestName {
          color: #ea580c;
        }
      }

      .chatItemHeader {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .guestInfo {
          .guestName {
            font-weight: 600;
            color: #111827;
            font-size: 15px;
            display: block;
            margin-bottom: 2px;
          }

          .roomInfo {
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
          }
        }

        .chatMeta {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;

          .priority {
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }

          .language {
            font-size: 11px;
            color: #6b7280;
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
          }
        }
      }

      .lastMessage {
        font-size: 14px;
        color: #4b5563;
        line-height: 1.4;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .chatItemFooter {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .time {
          font-size: 12px;
          color: #9ca3af;
        }

        .badges {
          display: flex;
          align-items: center;
          gap: 6px;

          .status {
            font-size: 10px;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            text-transform: capitalize;
          }

          .unreadBadge {
            background: #ef4444;
            color: white;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
          }
        }
      }
    }
  }
}

// Chat Area Styles
.chatArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.chatInterface {
  display: flex;
  flex-direction: column;
  height: 100%;

  .chatHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
    background: #fafafa;

    .chatHeaderLeft {
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin: 0 0 6px 0;
      }

      .chatHeaderMeta {
        display: flex;
        gap: 8px;
        align-items: center;

        .roomBadge {
          font-size: 12px;
          background: #dbeafe;
          color: #1e40af;
          padding: 3px 8px;
          border-radius: 6px;
          font-weight: 500;
        }

        .languageBadge {
          font-size: 12px;
          background: #f3e8ff;
          color: #7c3aed;
          padding: 3px 8px;
          border-radius: 6px;
          font-weight: 500;
        }

        .priorityBadge {
          font-size: 11px;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }
    }

    .chatActions {
      display: flex;
      gap: 8px;

      button {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #f3f4f6;
          border-color: #9ca3af;
        }

        &.translateButton {
          color: #059669;
          border-color: #d1fae5;
          background: #ecfdf5;

          &:hover, &.active {
            background: #d1fae5;
            border-color: #059669;
          }
        }

        &.transferButton {
          color: #0284c7;
          border-color: #dbeafe;
          background: #eff6ff;

          &:hover {
            background: #dbeafe;
          }
        }

        &.endButton {
          color: #dc2626;
          border-color: #fecaca;
          background: #fef2f2;

          &:hover {
            background: #fecaca;
          }
        }
      }
    }
  }

  .chatMessages {
    flex: 1;
    padding: 20px 24px;
    overflow-y: auto;
    background: #f9fafb;
    position: relative;

    .typingIndicator {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: #f3f4f6;
      border-radius: 12px;
      max-width: 200px;
      margin-bottom: 16px;
      font-size: 13px;
      color: #6b7280;

      .typingDots {
        display: flex;
        gap: 3px;

        span {
          width: 4px;
          height: 4px;
          background: #9ca3af;
          border-radius: 50%;
          animation: typingPulse 1.4s infinite;

          &:nth-child(2) {
            animation-delay: 0.2s;
          }

          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
    }
  }
}

@keyframes typingPulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.emptyChatArea {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f9fafb;

  .emptyState {
    text-align: center;
    color: #6b7280;
    max-width: 400px;

    .emptyIcon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #374151;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 16px;
      margin: 0 0 24px 0;
    }

    .statsGrid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-top: 24px;

      .statCard {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px;
        background: white;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .statNumber {
          font-size: 24px;
          font-weight: 700;
          color: #f97316;
          margin-bottom: 4px;
        }

        .statLabel {
          font-size: 12px;
          color: #6b7280;
          text-align: center;
        }
      }
    }
  }
}
// No Results Styling
.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
  
  .noResultsIcon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }
  
  p {
    font-size: 14px;
    margin: 0 0 16px 0;
    color: #9ca3af;
  }
  
  .clearFiltersButton {
    padding: 8px 16px;
    background: #f97316;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      background: #ea580c;
      transform: translateY(-1px);
    }
  }
}

// Enhanced sidebar styling
.sidebar {
  width: 380px;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  
  .sidebarHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 12px 24px;
    border-bottom: none; // Remove border since ChatSearch has its own
    
    h2 {
      font-size: 18px;
      font-weight: 600;
      color: #111827;
      margin: 0;
    }
    
    .chatCount {
      background: #f97316;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      transition: all 0.2s;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
  
  .chatList {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    
    .chatItem {
      padding: 16px;
      margin-bottom: 8px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s;
      border: 1px solid transparent;
      position: relative;
      
      // Add subtle animation on hover
      &:hover {
        background: #f8fafc;
        border-color: #e2e8f0;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }
      
      &.selected {
        background: #fef3e2;
        border-color: #f97316;
        box-shadow: 0 2px 8px rgba(249, 115, 22, 0.15);
        
        .guestName {
          color: #ea580c;
        }
        
        // Add selected indicator
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: #f97316;
          border-radius: 0 4px 4px 0;
        }
      }
      
      .chatItemHeader {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
        
        .guestInfo {
          .guestName {
            font-weight: 600;
            color: #111827;
            font-size: 15px;
            display: block;
            margin-bottom: 2px;
            transition: color 0.2s;
          }
          
          .roomInfo {
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            display: inline-block;
          }
        }
        
        .chatMeta {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;
          
          .priority {
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 2px 4px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.8);
          }
          
          .language {
            font-size: 11px;
            color: #6b7280;
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
          }
        }
      }
      
      .lastMessage {
        font-size: 14px;
        color: #4b5563;
        line-height: 1.4;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .chatItemFooter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .time {
          font-size: 12px;
          color: #9ca3af;
        }
        
        .badges {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .status {
            font-size: 10px;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            text-transform: capitalize;
          }
          
          .unreadBadge {
            background: #ef4444;
            color: white;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            animation: pulse 2s infinite;
          }
        }
      }
    }
  }
}

// Add pulse animation for unread badges
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}


// Responsive Design
@media (max-width: 1200px) {
  .sidebar {
    width: 320px;
  }
}

@media (max-width: 1024px) {
  .sidebar {
    width: 300px;
  }

  .header {
    .headerCenter {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    .mainContent {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      height: 250px;
      border-right: none;
      border-bottom: 1px solid #e2e8f0;
    }
  }

  .header {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;

    .headerLeft, .headerRight {
      width: 100%;
      justify-content: space-between;
    }

    .userInfo {
      order: -1;
    }
  }

  .chatInterface {
    .chatHeader {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .chatActions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
      }
    }
  }

  .emptyChatArea {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f9fafb;
  overflow-y: auto;
  padding: 20px;

  .emptyState {
    max-width: 500px;
    margin: 0 auto;
    width: 100%;

    .emptyContent {
      text-align: center;
      color: #6b7280;
      margin-bottom: 32px;

      .emptyIcon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.7;
      }

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #374151;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 16px;
        margin: 0 0 24px 0;
        color: #9ca3af;
      }
    }

    .statsGrid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 32px;

      .statCard {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        background: white;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .statNumber {
          font-size: 28px;
          font-weight: 700;
          color: #f97316;
          margin-bottom: 8px;
          line-height: 1;
        }

        .statLabel {
          font-size: 12px;
          color: #6b7280;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          font-weight: 500;
        }
      }
    }

    .quickActions {
      background: white;
      border-radius: 12px;
      padding: 24px;
      border: 1px solid #e5e7eb;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        margin: 0 0 16px 0;
        text-align: center;
      }

      .actionButtons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .actionButton {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          padding: 16px 12px;
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s;
          text-decoration: none;

          &:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
            transform: translateY(-1px);
          }

          .actionIcon {
            font-size: 24px;
          }

          .actionText {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
            text-align: center;
          }
        }
      }
    }
  }
}

}

// Scrollbar styling
.chatList, .chatMessages {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

// Animation utilities
.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.unreadIndicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

.chatItem {
  position: relative; // Add this to make absolute positioning work

  &.hasUnread {
    border-left: 4px solid #ef4444;
    background-color: rgba(239, 68, 68, 0.05);
  }
}

// Loading spinner styles for session messages
.loadingSpinner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;

  .spinner {
    width: 12px;
    height: 12px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #f97316;
    border-radius: 50%;
    animation: spinSmall 1s linear infinite;
  }
}

@keyframes spinSmall {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
