# 🧪 Setup Multiple Guests Testing - LoaLoa Chat

## 📋 **HƯỚNG DẪN THỰC HIỆN TỪNG BƯỚC**

### **BƯỚC 1: Tạo QR Codes trong Database**

1. **Mở Supabase Dashboard** của project LoaLoa
2. **Vào SQL Editor** (biểu tượng SQL ở sidebar)
3. **Copy và paste** nội dung file `create_test_qr_codes.sql`
4. **Click "Run"** để thực thi script
5. **Kiểm tra kết quả** - sẽ hiển thị các QR codes vừa tạo với URLs test

### **BƯỚC 2: Lấy Code Values từ Database**

Từ kết quả SQL ở bước 1, copy các `code_value` tương ứng:

```sql
-- Kết quả mẫu sẽ như thế này:
code_value                    | name                    | target_department
------------------------------|-------------------------|------------------
room102-test-1735123456       | Room 102 Guest Services| guest_services
restaurant-test-1735123457    | Restaurant Services     | food_beverage
spa-wellness-1735123458       | Spa & Wellness Center   | spa_wellness
concierge-test-1735123459     | Concierge Services      | concierge
housekeeping-test-1735123460  | Housekeeping Services   | housekeeping
```

### **BƯỚC 3: Cập nhật Test HTML File**

1. **Mở file** `test_multiple_guests.html`
2. **Tìm đoạn code** sau:

```javascript
const qrCodes = {
    room102: 'room102-test-placeholder',
    restaurant: 'restaurant-test-placeholder', 
    spa: 'spa-wellness-placeholder',
    concierge: 'concierge-test-placeholder',
    housekeeping: 'housekeeping-test-placeholder'
};
```

3. **Thay thế** bằng code_values thực tế từ database:

```javascript
const qrCodes = {
    room102: 'room102-test-1735123456',        // Thay bằng code_value thực tế
    restaurant: 'restaurant-test-1735123457',  // Thay bằng code_value thực tế
    spa: 'spa-wellness-1735123458',           // Thay bằng code_value thực tế
    concierge: 'concierge-test-1735123459',   // Thay bằng code_value thực tế
    housekeeping: 'housekeeping-test-1735123460' // Thay bằng code_value thực tế
};
```

### **BƯỚC 4: Test Multiple Guests**

1. **Mở Staff Dashboard** trước tiên:
   ```
   http://localhost:3003/staff/dashboard
   ```

2. **Mở file** `test_multiple_guests.html` trong browser

3. **Click từng Guest link** để mở trong tab riêng biệt:
   - 🏨 Guest 1 - Room 101 (existing): `http://localhost:3003/qr/mbblsqjm-i24r2?lang=en`
   - 🛏️ Guest 2 - Room 102: `http://localhost:3003/qr/room102-test-[timestamp]?lang=en`
   - 🍽️ Guest 3 - Restaurant: `http://localhost:3003/qr/restaurant-test-[timestamp]?lang=vi`
   - 🧘 Guest 4 - Spa: `http://localhost:3003/qr/spa-wellness-[timestamp]?lang=fr`
   - 🛎️ Guest 5 - Concierge: `http://localhost:3003/qr/concierge-test-[timestamp]?lang=ko`
   - 🧹 Guest 6 - Housekeeping: `http://localhost:3003/qr/housekeeping-test-[timestamp]?lang=ja`

4. **Gửi tin nhắn** từ mỗi guest để tạo chat sessions

5. **Quan sát Staff Dashboard** để test các tính năng:
   - ✅ Loading states khi chưa click vào session
   - ✅ Emoji 🔔 cho unread messages từ guests
   - ✅ Loading spinner khi đã click vào session
   - ✅ Multiple guest sessions cùng lúc
   - ✅ Real-time updates

## 🎯 **CÁC TÍNH NĂNG CẦN TEST**

### **1. Loading Message States:**
- [ ] Session mới hiển thị "No messages yet" thay vì "Loading messages..."
- [ ] Session đang load hiển thị "Connecting..." cho chưa viewed
- [ ] Session đang load hiển thị spinner cho đã viewed

### **2. Unread Message Indicators:**
- [ ] Emoji 🔔 xuất hiện cho tin nhắn mới từ guest chưa đọc
- [ ] Emoji biến mất khi staff click vào session
- [ ] Emoji xuất hiện lại khi có tin nhắn mới từ guest

### **3. Multiple Sessions:**
- [ ] Staff có thể xem nhiều guest sessions cùng lúc
- [ ] Switching giữa các sessions hoạt động mượt mà
- [ ] Real-time updates cho tất cả sessions
- [ ] Unread count chính xác cho từng session

### **4. UI/UX Consistency:**
- [ ] Design không bị phá vỡ
- [ ] Loading animations mượt mà
- [ ] Colors và styling giữ nguyên
- [ ] Responsive design vẫn hoạt động

## 🔧 **TROUBLESHOOTING**

### **Nếu QR codes không hoạt động:**
1. Kiểm tra `tenant_id` trong database có đúng không
2. Kiểm tra `reception_point_id` có tồn tại không
3. Kiểm tra `is_active = true` cho QR codes
4. Xem console logs trong browser để debug

### **Nếu Staff Dashboard không hiển thị sessions:**
1. Kiểm tra guest đã gửi tin nhắn chưa
2. Kiểm tra real-time connection status
3. Refresh Staff Dashboard
4. Kiểm tra network requests trong DevTools

### **Nếu Loading states không đúng:**
1. Clear browser cache
2. Kiểm tra JavaScript console errors
3. Kiểm tra state management trong React DevTools

## 📊 **DATABASE TABLES LIÊN QUAN**

| Bảng | Mục đích | Tự động tạo |
|-------|----------|-------------|
| `tenant_qr_codes` | QR codes mới | ✅ Script SQL |
| `tenant_message_reception_points` | Reception points | ✅ Script SQL |
| `temporary_users` | User tạm thời | ✅ Auto khi scan QR |
| `tenant_chat_sessions` | Chat sessions | ✅ Auto khi tạo chat |
| `tenant_chat_messages` | Tin nhắn | ✅ Auto khi gửi message |

## 🎉 **KẾT QUẢ MONG ĐỢI**

Sau khi hoàn thành, bạn sẽ có:
- ✅ 6 guest sessions khác nhau (Room 101, Room 102, Restaurant, Spa, Concierge, Housekeeping)
- ✅ Multiple languages testing (EN, VI, FR, KO, JA, etc.)
- ✅ Real-time chat giữa multiple guests và staff
- ✅ Loading states và unread indicators hoạt động đúng
- ✅ UI/UX không bị phá vỡ

## 📞 **HỖ TRỢ**

Nếu gặp vấn đề, kiểm tra:
1. Console logs trong browser
2. Network requests trong DevTools
3. Supabase logs trong dashboard
4. Database records trong SQL Editor
