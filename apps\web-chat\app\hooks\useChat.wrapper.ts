/**
 * Wrapper for useChat.enhanced to maintain backward compatibility
 */

import { useChatEnhanced } from './useChat.enhanced';

// Original interface for backward compatibility
interface UseChatProps {
  sessionId: string;
  guestId: string;
  autoTranslate: boolean;
  guestLanguage: string;
}

// Wrapper function that maintains the original API
export function useChat({ sessionId, guestId, autoTranslate, guestLanguage }: UseChatProps) {
  const enhancedResult = useChatEnhanced({
    sessionId,
    guestId,
    guestLanguage,
    autoTranslate
  });

  // Return only the original interface properties
  return {
    session: enhancedResult.session,
    messages: enhancedResult.messages,
    loading: enhancedResult.loading,
    connected: enhancedResult.connected,
    sendMessage: enhancedResult.sendMessage,
    isTyping: enhancedResult.isTyping,
    startTyping: enhancedResult.startTyping,
    stopTyping: enhancedResult.stopTyping,
    setAutoTranslate: enhancedResult.setAutoTranslate,
    error: enhancedResult.error,
    realtimeConnected: enhancedResult.realtimeConnected,
    usePollingFallback: enhancedResult.usePollingFallback,
    
    // Enhanced features (optional)
    getPerformanceMetrics: enhancedResult.getPerformanceMetrics,
    logPerformance: enhancedResult.logPerformance,
  };
}

export default useChat;
