'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import ChatMessage from './components/ChatMessage';
import ChatInput from './components/ChatInput';
import styles from './dashboard.module.scss';
import NotificationCenter from './components/notifications/NotificationCenter';
import StaffStatus from './components/StaffStatus';
import ChatSearch from './components/ChatSearch';
import ChatStats from './components/ChatStats';
import QuickActions from './components/QuickActions';
import { createClientSupabase } from '../../lib/supabase';

interface User {
  id: string;
  email: string;
  display_name: string;
  role: string;
  tenant_id: string;
  department?: string;
  title?: string;
  avatar_url?: string;
  reception_points?: any[];
}

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
  session_ids?: string[]; // Add this to store multiple session IDs
}

interface Message {
  id: string;
  content: string;
  original_content?: string;
  translated_content?: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
  is_translated: boolean;
  original_language?: string;
  translated_language?: string;
  translation_confidence?: number;
  show_translation: boolean;
}

export default function StaffDashboard() {
  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineStatus, setOnlineStatus] = useState<'online' | 'busy' | 'away'>('online');
  const [autoTranslate, setAutoTranslate] = useState(true);
  // State management for session loading and viewing
  const [sessionLoadingStates, setSessionLoadingStates] = useState<{[sessionId: string]: boolean}>({});
  const [sessionViewedStates, setSessionViewedStates] = useState<{[sessionId: string]: boolean}>({});
  const [lastViewedTime, setLastViewedTime] = useState<{[sessionId: string]: number}>({});
  const [isTyping, setIsTyping] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
const [chatFilters, setChatFilters] = useState({
  status: 'all' as 'all' | 'active' | 'pending' | 'waiting',
  priority: 'all' as 'all' | 'low' | 'normal' | 'high' | 'urgent',
  language: 'all' as 'all' | 'en' | 'vi' | 'ko' | 'ja' | 'es' | 'fr' | 'de' | 'th' | 'id',
  timeRange: 'all' as 'all' | 'today' | 'week' | 'month',
  unreadOnly: false
});

const [lastMessageCheck, setLastMessageCheck] = useState<number>(Date.now());
const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true);
const smartRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
const sessionListRefreshRef = useRef<NodeJS.Timeout | null>(null);

// Realtime state
const [realtimeConnected, setRealtimeConnected] = useState(false);
const supabaseRef = useRef<any>(null);
const subscriptionsRef = useRef<any[]>([]);

const scrollToBottom = () => {
  messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
};

const checkForNewMessages = async () => {
  if (!user || activeChatSessions.length === 0) return;
  
  try {
    // Check for new messages in all active sessions
    for (const session of activeChatSessions) {
      const response = await fetch(`/api/messages?session_id=${session.id}&limit=1&offset=0`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.messages && data.messages.length > 0) {
          const latestMessage = data.messages[0];
          
          // Check if this is a new guest message
          if (latestMessage.sender_type === 'guest' && 
              new Date(latestMessage.created_at).getTime() > new Date(session.last_message_time).getTime()) {
            
            // Update session with new message and increment unread count
            setActiveChatSessions(prev => prev.map(s => 
              s.id === session.id ? {
                ...s,
                last_message: latestMessage.content.substring(0, 50) + (latestMessage.content.length > 50 ? '...' : ''),
                last_message_time: formatTimeAgo(latestMessage.created_at),
                unread_count: s.unread_count + 1
              } : s
            ));
            
            // If this session is currently selected, refresh messages
            if (selectedSession === session.id) {
              loadRealMessages(session.id);
            }
            
            // Show browser notification
            if (Notification.permission === 'granted') {
              new Notification(`New message from ${session.guest_name}`, {
                body: latestMessage.content.substring(0, 100),
                icon: '/favicon.ico'
              });
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('❌ Error checking for new messages:', error);
  }
};

// Request notification permission
useEffect(() => {
  if (user && Notification.permission === 'default') {
    Notification.requestPermission().then(permission => {
      console.log('Notification permission:', permission);
    });
  }
}, [user]);

// Helper function to format time ago
const formatTimeAgo = (timestamp: string): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} minutes ago`;
  if (diffHours < 24) return `${diffHours} hours ago`;
  return `${diffDays} days ago`;
};

// Define refreshSessionList first (before it's used in dependencies)
const refreshSessionList = useCallback(async () => {
  if (!user || !isAutoRefreshEnabled) return;

  try {
    console.log('🔄 Refreshing session list...');

    const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}&status=active&limit=100`);

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.sessions) {
        // Group sessions by guest (same logic as loadRealChatSessions)
        const sessionGroups = new Map<string, any[]>();

        data.sessions.forEach((session: any) => {
          const groupKey = session.qr_info?.room_number ||
                          session.reception_point?.name ||
                          session.qr_info?.location ||
                          'unknown';

          if (!sessionGroups.has(groupKey)) {
            sessionGroups.set(groupKey, []);
          }
          sessionGroups.get(groupKey)!.push(session);
        });

        // Transform grouped sessions to UI format
        const transformedSessions: ChatSession[] = [];

        for (const [groupKey, sessions] of sessionGroups) {
          const latestSession = sessions.sort((a, b) =>
            new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
          )[0];

          const sessionIds = sessions.map(s => s.id);

          // Check if we already have this session to preserve last_message and unread_count
          const existingSession = activeChatSessions.find(s => s.id === latestSession.id);

          const transformedSession: ChatSession = {
            id: latestSession.id,
            guest_name: latestSession.qr_info?.room_number ?
              `Room ${latestSession.qr_info.room_number} Guest` :
              'Guest User',
            room_number: latestSession.qr_info?.room_number || undefined,
            language: latestSession.guest_language?.toUpperCase() || 'EN',
            status: latestSession.status as 'active' | 'pending' | 'waiting',
            priority: latestSession.priority as 'low' | 'normal' | 'high' | 'urgent',
            // Preserve existing last_message and unread_count if available
            last_message: existingSession?.last_message || null, // Set null để handle riêng
            last_message_time: existingSession?.last_message_time || formatTimeAgo(latestSession.updated_at),
            unread_count: existingSession?.unread_count || 0,
            source: latestSession.qr_info?.location || latestSession.reception_point?.name || 'Direct',
            session_ids: sessionIds
          };

          transformedSessions.push(transformedSession);
        }

        setActiveChatSessions(transformedSessions);
        console.log('✅ Session list refreshed');
      }
    }
  } catch (error) {
    console.error('❌ Error refreshing session list:', error);
  }
}, [user, isAutoRefreshEnabled]);

// Setup realtime subscriptions for staff dashboard
const setupRealtimeSubscriptions = useCallback(() => {
  if (!supabaseRef.current || !user) return;

  console.log('🔄 Staff Dashboard: Setting up realtime subscriptions');

  try {
    // Messages subscription for all chat sessions
    const messagesChannel = supabaseRef.current
      .channel('staff-messages')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tenant_chat_messages',
          filter: `tenant_id=eq.${user.tenant_id}`
        },
        (payload: any) => {
          console.log('🔔 Staff Dashboard: New message received via realtime:', payload.new);
          console.log('🔍 Message details:', {
            id: payload.new.id,
            session_id: payload.new.chat_session_id,
            sender_type: payload.new.sender_type,
            content: payload.new.content?.substring(0, 50),
            tenant_id: payload.new.tenant_id
          });

          // If this message is for the selected session, add it immediately
          if (selectedSession && payload.new.chat_session_id === selectedSession) {
            const newMessage = {
              id: payload.new.id,
              content: payload.new.content,
              sender_type: payload.new.sender_type,
              sender_name: payload.new.sender_name || (payload.new.sender_type === 'guest' ? 'Guest' : 'Staff'),
              timestamp: payload.new.created_at,
              is_translated: payload.new.is_translated || false,
              show_translation: false
            };

            setMessages(prev => {
              const exists = prev.some(msg => msg.id === newMessage.id);
              if (exists) return prev;
              console.log('➕ Adding new message to chat:', newMessage.id);
              return [...prev, newMessage];
            });

            setTimeout(scrollToBottom, 100);
          }

          // Update session list immediately with new message info
          const sessionId = payload.new.chat_session_id;
          setActiveChatSessions(prev => {
            const updated = prev.map(session => {
              // Check if this message belongs to this session (direct or grouped)
              const belongsToSession = session.id === sessionId ||
                (session.session_ids && session.session_ids.includes(sessionId));

              if (belongsToSession) {
                console.log('🔄 Updating session:', session.id, 'with new message from:', payload.new.sender_type);
                const updatedSession = {
                  ...session,
                  last_message: payload.new.content.substring(0, 50) + (payload.new.content.length > 50 ? '...' : ''),
                  last_message_time: formatTimeAgo(payload.new.created_at),
                  unread_count: payload.new.sender_type === 'guest' &&
                                session.id !== selectedSession &&
                                !sessionViewedStates[session.id]
                    ? session.unread_count + 1
                    : session.unread_count
                };

                // Reset viewed state khi có tin nhắn mới từ guest (chỉ nếu không phải session hiện tại)
                if (payload.new.sender_type === 'guest' && session.id !== selectedSession) {
                  setSessionViewedStates(prev => ({
                    ...prev,
                    [session.id]: false
                  }));
                }

                return updatedSession;
              }
              return session;
            });

            return updated;
          });

          // Show notification for guest messages
          if (payload.new.sender_type === 'guest' && document.hidden) {
            if (Notification.permission === 'granted') {
              new Notification('New message from guest', {
                body: payload.new.content.substring(0, 100),
                icon: '/favicon.ico'
              });
            }
          }
        }
      )
      .subscribe((status: string) => {
        console.log('📡 Staff Dashboard: Messages subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('✅ Staff Dashboard: Realtime messages subscription active');
          console.log('🔍 Listening for messages with filter: tenant_id=eq.' + user.tenant_id);
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Staff Dashboard: Messages subscription error');
        }
      });

    // Sessions subscription for new chats and status updates
    const sessionsChannel = supabaseRef.current
      .channel('staff-sessions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tenant_chat_sessions',
          filter: `tenant_id=eq.${user.tenant_id}`
        },
        (payload: any) => {
          console.log('🔄 Staff Dashboard: Session updated via realtime:', payload);

          // Refresh session list when sessions change
          setTimeout(refreshSessionList, 200);

          // Also refresh message counts for existing sessions
          setTimeout(() => {
            if (activeChatSessions.length > 0) {
              loadMessageCounts(activeChatSessions);
            }
          }, 1000);
        }
      )
      .subscribe((status: string) => {
        console.log('📡 Staff Dashboard: Sessions subscription status:', status);
      });

    // Store subscriptions for cleanup
    subscriptionsRef.current = [messagesChannel, sessionsChannel];

  } catch (err) {
    console.error('❌ Staff Dashboard: Failed to setup realtime subscriptions:', err);
  }
}, [user, selectedSession, refreshSessionList]);

// Initialize Supabase Realtime
const initializeRealtime = useCallback(() => {
  try {
    supabaseRef.current = createClientSupabase();
    setRealtimeConnected(true);
    console.log('✅ Staff Dashboard: Supabase realtime initialized');

    // Test connection
    setTimeout(() => {
      if (supabaseRef.current) {
        console.log('🔍 Testing Supabase connection...');
        console.log('📊 Supabase client status:', {
          url: supabaseRef.current.supabaseUrl,
          key: supabaseRef.current.supabaseKey ? 'Present' : 'Missing'
        });
      }
    }, 1000);
  } catch (err) {
    console.error('❌ Staff Dashboard: Failed to initialize Supabase realtime:', err);
    setRealtimeConnected(false);
  }
}, []);

// Cleanup subscriptions (defined before use)
const cleanupSubscriptions = useCallback(() => {
  console.log('🧹 Staff Dashboard: Cleaning up realtime subscriptions');
  subscriptionsRef.current.forEach(subscription => {
    try {
      subscription.unsubscribe();
    } catch (err) {
      console.warn('Warning: Failed to unsubscribe:', err);
    }
  });
  subscriptionsRef.current = [];
  setRealtimeConnected(false);
}, []);

// Replace periodic check with realtime setup
useEffect(() => {
  if (user && supabaseRef.current && realtimeConnected) {
    setupRealtimeSubscriptions();

    return () => {
      cleanupSubscriptions();
    };
  }
}, [user, realtimeConnected, setupRealtimeSubscriptions, cleanupSubscriptions]);

 useEffect(() => {
  // Check authentication
  const token = localStorage.getItem('staff_token');
  const userData = localStorage.getItem('staff_user');

  if (!token || !userData) {
    router.push('/staff');
    return;
  }

  try {
    const parsedUser = JSON.parse(userData);
    setUser(parsedUser);

    // Initialize Supabase realtime
    initializeRealtime();
  } catch (error) {
    console.error('Error parsing user data:', error);
    router.push('/staff');
  } finally {
    setLoading(false);
  }
}, [router]);

useEffect(() => {
  if (user) {
    loadRealChatSessions();
  }
}, [user]);

// Load real chat sessions
const loadRealChatSessions = async () => {
  if (!user) return;
  
  try {
    console.log('🔄 Loading real chat sessions for tenant:', user.tenant_id);
    
    const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}&status=active&limit=100`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.sessions) {
        console.log('✅ Loaded sessions:', data.sessions.length);
        
        // Group sessions by guest (room_number or source)
        const sessionGroups = new Map<string, any[]>();
        
        data.sessions.forEach((session: any) => {
          const groupKey = session.qr_info?.room_number || 
                          session.reception_point?.name || 
                          session.qr_info?.location || 
                          'unknown';
          
          if (!sessionGroups.has(groupKey)) {
            sessionGroups.set(groupKey, []);
          }
          sessionGroups.get(groupKey)!.push(session);
        });
        
        // Transform grouped sessions to UI format
        const transformedSessions: ChatSession[] = [];
        
        for (const [groupKey, sessions] of sessionGroups) {
          // Use the most recent session for this group
          const latestSession = sessions.sort((a, b) => 
            new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
          )[0];
          
          // Collect all session IDs for this group
          const sessionIds = sessions.map(s => s.id);
          
          const transformedSession: ChatSession = {
            id: latestSession.id,
            guest_name: latestSession.qr_info?.room_number ?
              `Room ${latestSession.qr_info.room_number} Guest` :
              'Guest User',
            room_number: latestSession.qr_info?.room_number || undefined,
            language: latestSession.guest_language?.toUpperCase() || 'EN',
            status: latestSession.status as 'active' | 'pending' | 'waiting',
            priority: latestSession.priority as 'low' | 'normal' | 'high' | 'urgent',
            last_message: null, // Will be updated with real last message
            last_message_time: formatTimeAgo(latestSession.updated_at),
            unread_count: 0, // Will be calculated from real messages
            source: latestSession.qr_info?.location || latestSession.reception_point?.name || 'Direct',
            // Store all session IDs for this guest
            session_ids: sessionIds
          };
          
          transformedSessions.push(transformedSession);
        }
        
        setActiveChatSessions(transformedSessions);

        // Load message counts for each session (including single sessions)
        loadMessageCounts(transformedSessions);
      } else {
        console.warn('⚠️ No sessions found or invalid response');
        setActiveChatSessions([]);
      }
    } else {
      console.error('❌ Failed to load sessions:', response.status);
      setActiveChatSessions([]);
    }
  } catch (error) {
    console.error('❌ Error loading chat sessions:', error);
    setActiveChatSessions([]);
  }
};

// Load message counts and last messages for sessions
const loadMessageCounts = async (sessions: ChatSession[]) => {
  // Load for ALL sessions, not just grouped ones
  console.log('🔍 Loading message counts for', sessions.length, 'sessions');

  for (const session of sessions) {
    // Skip if session is currently selected or viewed (prevent override)
    if (session.id === selectedSession || sessionViewedStates[session.id]) {
      console.log('⏭️ Skipping message count for viewed session:', session.id);
      continue;
    }

    try {
      // Get all session IDs for this guest (including single sessions)
      const sessionIds = session.session_ids && session.session_ids.length > 0
        ? session.session_ids
        : [session.id];

      // Load messages from all sessions for this guest
      const allMessages: any[] = [];

      for (const sessionId of sessionIds) {
        const response = await fetch(`/api/messages?session_id=${sessionId}&limit=20&offset=0`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.messages) {
            allMessages.push(...data.messages);
          }
        }
      }

      if (allMessages.length > 0) {
        // Sort messages by timestamp to get the latest
        allMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        const lastMessage = allMessages[allMessages.length - 1];

        // Count unread messages (guest messages only, exclude if currently selected or viewed)
        const unreadCount = session.id === selectedSession || sessionViewedStates[session.id]
          ? 0
          : allMessages.filter((msg: any) =>
              msg.sender_type === 'guest' &&
              new Date(msg.created_at).getTime() > (lastViewedTime[session.id] || 0)
            ).length;

        // Update session with real last message and unread count
        setActiveChatSessions(prev => prev.map(s =>
          s.id === session.id ? {
            ...s,
            last_message: lastMessage.content.substring(0, 50) + (lastMessage.content.length > 50 ? '...' : ''),
            last_message_time: formatTimeAgo(lastMessage.created_at),
            unread_count: unreadCount
          } : s
        ));
      } else {
        // No messages found, keep default values but update them
        setActiveChatSessions(prev => prev.map(s =>
          s.id === session.id ? {
            ...s,
            last_message: 'No messages yet',
            last_message_time: formatTimeAgo(new Date().toISOString()),
            unread_count: 0
          } : s
        ));
      }
    } catch (error) {
      console.error(`❌ Error loading messages for session ${session.id}:`, error);
    }
  }
};
// Load real messages for selected session
const loadRealMessages = async (sessionId: string) => {
  try {
    console.log('📨 Loading real messages for session:', sessionId);
    
    // Find the session data to get all session IDs for this guest
    const sessionData = activeChatSessions.find(s => s.id === sessionId);
    const sessionIds = sessionData?.session_ids || [sessionId];
    
    console.log('🔍 Loading messages for session IDs:', sessionIds);
    
    // Load messages for all sessions of this guest
    const allMessages: any[] = [];
    
    for (const sId of sessionIds) {
      const response = await fetch(`/api/messages?session_id=${sId}&limit=100`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.messages) {
          allMessages.push(...data.messages);
        }
      }
    }
    
    // Sort all messages by timestamp
    allMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    
    console.log('✅ Loaded total messages:', allMessages.length);
    
    // Transform API messages to match UI interface
    const transformedMessages: Message[] = allMessages.map((msg: any) => ({
      id: msg.id,
      content: msg.translated_content && msg.show_translation ? 
        msg.translated_content : msg.content,
      original_content: msg.original_content || msg.content,
      translated_content: msg.translated_content,
      sender_type: msg.sender_type,
      sender_name: msg.sender_name || (msg.sender_type === 'guest' ? 'Guest' : 'Staff'),
      timestamp: msg.created_at,
      is_translated: msg.is_translated || false,
      original_language: msg.original_language,
      translated_language: msg.translated_language,
      translation_confidence: msg.translation_confidence,
      show_translation: autoTranslate && msg.is_translated
    }));
    
    setMessages(transformedMessages);

    // Update session with real last message
    if (allMessages.length > 0) {
      const lastMessage = allMessages[allMessages.length - 1];
      setActiveChatSessions(sessions => sessions.map(session =>
        session.id === sessionId ? {
          ...session,
          last_message: lastMessage.content.substring(0, 50) + (lastMessage.content.length > 50 ? '...' : ''),
          unread_count: 0
        } : session
      ));
    } else {
      // No messages found
      setActiveChatSessions(sessions => sessions.map(session =>
        session.id === sessionId ? {
          ...session,
          last_message: 'No messages yet',
          unread_count: 0
        } : session
      ));
    }

  } catch (error) {
    console.error('❌ Error loading messages:', error);
    setMessages([]);
  }
};
  const loadMockMessages = (sessionId: string) => {
    // Mock messages for selected session - Tin nhắn mẫu cho session được chọn
    const mockMessages: Message[] = [
      {
        id: 'msg-1',
        content: 'Hello, I need help with the air conditioning in my room.',
        original_content: 'Hello, I need help with the air conditioning in my room.',
        translated_content: 'Xin chào, tôi cần giúp đỡ với máy lạnh trong phòng của tôi.',
        sender_type: 'guest',
        sender_name: 'John Smith',
        timestamp: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        is_translated: true,
        original_language: 'en',
        translated_language: 'vi',
        translation_confidence: 0.95,
        show_translation: autoTranslate
      },
      {
        id: 'msg-2',
        content: 'Hi John! I\'ll help you with that right away. What seems to be the issue with the air conditioning?',
        sender_type: 'staff',
        sender_name: user?.display_name || 'Staff',
        timestamp: new Date(Date.now() - 240000).toISOString(), // 4 minutes ago
        is_translated: false,
        show_translation: false
      },
      {
        id: 'msg-3',
        content: 'It\'s not cooling properly and making strange noises.',
        original_content: 'It\'s not cooling properly and making strange noises.',
        translated_content: 'Nó không làm mát đúng cách và tạo ra những tiếng động lạ.',
        sender_type: 'guest',
        sender_name: 'John Smith',
        timestamp: new Date(Date.now() - 180000).toISOString(), // 3 minutes ago
        is_translated: true,
        original_language: 'en',
        translated_language: 'vi',
        translation_confidence: 0.92,
        show_translation: autoTranslate
      },
      {
        id: 'msg-4',
        content: 'I understand. I\'ll send our maintenance team to check it immediately. They should be there within 10 minutes.',
        sender_type: 'staff',
        sender_name: user?.display_name || 'Staff',
        timestamp: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
        is_translated: false,
        show_translation: false
      }
    ];

    setMessages(mockMessages);
  };

  const handleLogout = () => {
    localStorage.removeItem('staff_token');
    localStorage.removeItem('staff_user');
    router.push('/staff');
  };

  const handleStatusChange = (status: 'online' | 'busy' | 'away') => {
    setOnlineStatus(status);
    // TODO: Update status in backend
  };

// Smart refresh function chỉ check tin nhắn mới
const smartRefreshMessages = useCallback(async () => {
  if (!selectedSession || !isAutoRefreshEnabled) return;

  try {
    const sessionData = activeChatSessions.find(s => s.id === selectedSession);
    const sessionIds = sessionData?.session_ids || [selectedSession];
    
    // Load messages for all sessions of this guest
    const allMessages: any[] = [];
    
    for (const sId of sessionIds) {
      const response = await fetch(`/api/messages?session_id=${sId}&limit=20`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.messages) {
          allMessages.push(...data.messages);
        }
      }
    }
    
    // Sort all messages by timestamp
    allMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    
    if (allMessages.length > 0) {
      const latestMessage = allMessages[allMessages.length - 1];
      const latestMessageTime = new Date(latestMessage.created_at).getTime();
      
      // Check if we have new messages since last check
      if (latestMessageTime > lastMessageCheck) {
        console.log('🔔 New message detected in chat, refreshing...');
        
        // Transform and update messages
        const transformedMessages: Message[] = allMessages.map((msg: any) => ({
          id: msg.id,
          content: msg.translated_content && msg.show_translation ? 
            msg.translated_content : msg.content,
          original_content: msg.original_content || msg.content,
          translated_content: msg.translated_content,
          sender_type: msg.sender_type,
          sender_name: msg.sender_name || (msg.sender_type === 'guest' ? 'Guest' : 'Staff'),
          timestamp: msg.created_at,
          is_translated: msg.is_translated || false,
          original_language: msg.original_language,
          translated_language: msg.translated_language,
          translation_confidence: msg.translation_confidence,
          show_translation: autoTranslate && msg.is_translated
        }));
        
        setMessages(transformedMessages);
        setLastMessageCheck(Date.now());
        
        // Auto scroll to bottom
        setTimeout(scrollToBottom, 100);
        
        // Show notification for new guest messages
        if (latestMessage.sender_type === 'guest' && document.hidden) {
          if (Notification.permission === 'granted') {
            new Notification(`New message from guest`, {
              body: latestMessage.content.substring(0, 100),
              icon: '/favicon.ico'
            });
          }
        }
        
        // Refresh session list to update last message
        setTimeout(refreshSessionList, 500);
      }
    }
  } catch (error) {
    console.error('❌ Smart refresh messages error:', error);
  }
}, [selectedSession, isAutoRefreshEnabled, lastMessageCheck, activeChatSessions, autoTranslate, refreshSessionList]);

// Start smart refresh for selected session
const startSmartRefresh = useCallback(() => {
  if (smartRefreshIntervalRef.current) {
    clearInterval(smartRefreshIntervalRef.current);
  }

  if (selectedSession && isAutoRefreshEnabled) {
    smartRefreshIntervalRef.current = setInterval(() => {
      if (!document.hidden) { // Only refresh when page is visible
        smartRefreshMessages();
      }
    }, 3000); // Check every 3 seconds
  }
}, [selectedSession, isAutoRefreshEnabled, smartRefreshMessages]);
// Stop smart refresh
const stopSmartRefresh = useCallback(() => {
  if (smartRefreshIntervalRef.current) {
    clearInterval(smartRefreshIntervalRef.current);
    smartRefreshIntervalRef.current = null;
  }
}, []);

const [filteredChatSessions, setFilteredChatSessions] = useState<ChatSession[]>([]);

  // Helper function để xác định hiển thị message
  const getSessionMessageDisplay = (session: ChatSession) => {
    const isLoading = sessionLoadingStates[session.id];
    const isViewed = sessionViewedStates[session.id];
    const hasUnread = session.unread_count > 0;

    // Nếu có tin nhắn thực
    if (session.last_message) {
      const messageText = session.last_message;

      // Nếu có unread và chưa viewed → thêm emoji
      if (hasUnread && !isViewed) {
        return `🔔 ${messageText}`;
      }

      return messageText;
    }

    // Nếu đang loading và đã viewed → hiển thị spinner
    if (isLoading && isViewed) {
      return null; // Sẽ render loading component
    }

    // Nếu đang loading và chưa viewed → hiển thị status
    if (isLoading) {
      return 'Connecting...';
    }

    // Fallback
    return 'No messages yet';
  };

  const handleChatSelect = (sessionId: string) => {
    setSelectedSession(sessionId);
    setLastMessageCheck(Date.now()); // Reset check time

    // Mark session as viewed with timestamp
    const viewTime = Date.now();
    setSessionViewedStates(prev => ({
      ...prev,
      [sessionId]: true
    }));

    setLastViewedTime(prev => ({
      ...prev,
      [sessionId]: viewTime
    }));

    // Mark session as read immediately
    setActiveChatSessions(prev => prev.map(session =>
      session.id === sessionId ? { ...session, unread_count: 0 } : session
    ));

    // Start loading messages for this session
    setSessionLoadingStates(prev => ({
      ...prev,
      [sessionId]: true
    }));

    // Load real messages
    loadRealMessages(sessionId).finally(() => {
      setSessionLoadingStates(prev => ({
        ...prev,
        [sessionId]: false
      }));
    });

    // Start smart refresh for this session
    setTimeout(() => {
      startSmartRefresh();
    }, 1000);
  };

  // Update handleSendMessage to trigger immediate refresh
const handleSendMessage = async (content: string) => {
  if (!selectedSession || !user) return;

  try {
    console.log('📤 Sending staff message to session:', selectedSession);
    
    const response = await fetch('/api/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: selectedSession,
        content: content.trim(),
        sender_type: 'staff',
        sender_name: user.display_name,
      }),
    });

    const data = await response.json();

    if (response.ok && data.success && data.message) {
      console.log('✅ Staff message sent successfully');
      
      // Add message to UI immediately
      const newMessage: Message = {
        id: data.message.id,
        content: data.message.content,
        sender_type: 'staff',
        sender_name: user.display_name,
        timestamp: data.message.created_at,
        is_translated: false,
        show_translation: false
      };
      
      setMessages(prev => [...prev, newMessage]);
      setLastMessageCheck(Date.now());
      
      setTimeout(scrollToBottom, 100);
      
      // Update session list immediately with staff message
      setActiveChatSessions(prev => prev.map(session => {
        const belongsToSession = session.id === selectedSession ||
          (session.session_ids && session.session_ids.includes(selectedSession));

        if (belongsToSession) {
          return {
            ...session,
            last_message: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
            last_message_time: formatTimeAgo(data.message.created_at)
          };
        }
        return session;
      }));

      // Check for guest reply after a delay
      setTimeout(smartRefreshMessages, 2000);
      
    } else {
      console.error('❌ Send failed:', data);
      throw new Error(data.error || 'Failed to send message');
    }
  } catch (error) {
    console.error('❌ Error sending message:', error);
    alert('Failed to send message: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
};

useEffect(() => {
  if (user && isAutoRefreshEnabled) {
    // Refresh session list every 10 seconds
    if (sessionListRefreshRef.current) {
      clearInterval(sessionListRefreshRef.current);
    }

    sessionListRefreshRef.current = setInterval(() => {
      if (!document.hidden) {
        console.log('🔄 Periodic session list refresh...');
        refreshSessionList();

        // Also refresh message counts to catch any missed updates
        if (activeChatSessions.length > 0) {
          setTimeout(() => {
            loadMessageCounts(activeChatSessions);
          }, 1000);
        }
      }
    }, 10000); // Every 10 seconds

    return () => {
      if (sessionListRefreshRef.current) {
        clearInterval(sessionListRefreshRef.current);
      }
    };
  }
}, [user, isAutoRefreshEnabled, refreshSessionList, activeChatSessions]);

// Realtime replaces smart refresh - only use polling as fallback
useEffect(() => {
  if (selectedSession && isAutoRefreshEnabled && !realtimeConnected) {
    console.log('⚠️ Staff Dashboard: Using polling fallback (realtime not connected)');

    if (smartRefreshIntervalRef.current) {
      clearInterval(smartRefreshIntervalRef.current);
    }

    smartRefreshIntervalRef.current = setInterval(() => {
      if (!document.hidden) {
        smartRefreshMessages();
      }
    }, 10000); // Slower polling as fallback (10 seconds)

    return () => {
      if (smartRefreshIntervalRef.current) {
        clearInterval(smartRefreshIntervalRef.current);
      }
    };
  } else if (realtimeConnected && smartRefreshIntervalRef.current) {
    // Stop polling when realtime is connected
    console.log('✅ Staff Dashboard: Stopping polling (realtime connected)');
    clearInterval(smartRefreshIntervalRef.current);
    smartRefreshIntervalRef.current = null;
  }
}, [selectedSession, isAutoRefreshEnabled, realtimeConnected, smartRefreshMessages]);

// Handle page visibility changes
useEffect(() => {
  const handleVisibilityChange = () => {
    if (!document.hidden) {
      // Page became visible, refresh everything
      if (isAutoRefreshEnabled) {
        refreshSessionList();
        if (selectedSession) {
          setTimeout(smartRefreshMessages, 500);
        }
      }
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);
  return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
}, [isAutoRefreshEnabled, refreshSessionList, selectedSession, smartRefreshMessages]);
  const handleToggleTranslation = () => {
    setAutoTranslate(!autoTranslate);
    // Update all messages to show/hide translation
    setMessages(prev =>
      prev.map(msg => ({
        ...msg,
        show_translation: !autoTranslate
      }))
    );
  };

  const handleTransferChat = () => {
    // TODO: Implement chat transfer
    alert('Chat transfer feature coming soon!');
  };

  const handleEndChat = () => {
    if (selectedSession && confirm('Are you sure you want to end this chat?')) {
      setActiveChatSessions(sessions =>
        sessions.filter(session => session.id !== selectedSession)
      );
      setSelectedSession(null);
      setMessages([]);
    }
  };
  
  const handleSearch = (query: string) => {
  setSearchQuery(query);
  applyFiltersAndSearch(activeChatSessions, query, chatFilters);
};

const handleFilter = (filters: typeof chatFilters) => {
  setChatFilters(filters);
  applyFiltersAndSearch(activeChatSessions, searchQuery, filters);
};

const applyFiltersAndSearch = (
  sessions: ChatSession[], 
  query: string, 
  filters: typeof chatFilters
) => {
  let filtered = [...sessions];

  // Apply search query - Áp dụng tìm kiếm
  if (query.trim()) {
    const lowerQuery = query.toLowerCase();
    filtered = filtered.filter(session =>
      session.guest_name.toLowerCase().includes(lowerQuery) ||
      session.room_number?.toLowerCase().includes(lowerQuery) ||
      session.source.toLowerCase().includes(lowerQuery) ||
      (session.last_message && session.last_message.toLowerCase().includes(lowerQuery))
    );
  }

  // Apply status filter - Áp dụng lọc trạng thái
  if (filters.status !== 'all') {
    filtered = filtered.filter(session => session.status === filters.status);
  }

  // Apply priority filter - Áp dụng lọc độ ưu tiên
  if (filters.priority !== 'all') {
    filtered = filtered.filter(session => session.priority === filters.priority);
  }

  // Apply language filter - Áp dụng lọc ngôn ngữ  
  if (filters.language !== 'all') {
    filtered = filtered.filter(session => 
      session.language.toLowerCase() === filters.language
    );
  }

  // Apply unread filter - Áp dụng lọc tin nhắn chưa đọc
  if (filters.unreadOnly) {
    filtered = filtered.filter(session => session.unread_count > 0);
  }

  // Apply time range filter - Áp dụng lọc thời gian
  if (filters.timeRange !== 'all') {
    const now = new Date();
    filtered = filtered.filter(session => {
      // Mock time filter logic - Logic lọc thời gian mẫu
      const sessionTime = new Date(session.last_message_time);
      
      switch (filters.timeRange) {
        case 'today':
          return sessionTime.toDateString() === now.toDateString();
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          return sessionTime >= weekAgo;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          return sessionTime >= monthAgo;
        default:
          return true;
      }
    });
  }

  setFilteredChatSessions(filtered);
};


  const handleNotificationClick = (notification: any) => {
  // Handle notification click - Xử lý click notification
  console.log('Notification clicked:', notification);
  
  // If it's a chat-related notification, switch to that session
  if (notification.type === 'new_message' || notification.type === 'new_chat') {
    // Find related session and select it
    const relatedSession = activeChatSessions.find(session => 
      notification.message.includes(session.guest_name) ||
      notification.message.includes(session.room_number)
    );
    
    if (relatedSession) {
      handleChatSelect(relatedSession.id);
    }
  }
};

  const handleQuickAction = (action: string) => {
  // Handle quick actions - Xử lý quick actions
  console.log('Quick action:', action);
  
  switch (action) {
    case 'broadcast':
      alert('Broadcast message feature coming soon!');
      break;
    case 'templates':
      alert('Quick templates feature coming soon!');
      break;
    case 'transfer':
      alert('Transfer all chats feature coming soon!');
      break;
    case 'reports':
      alert('Generate report feature coming soon!');
      break;
    case 'settings':
      alert('Chat settings feature coming soon!');
      break;
    case 'break':
      handleStatusChange('away');
      break;
    default:
      console.log('Unknown action:', action);
  }
};


  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#ef4444';
      case 'high': return '#f97316';
      case 'normal': return '#3b82f6';
      case 'low': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'waiting': return '#ef4444';
      default: return '#6b7280';
    }
  };

  // Cleanup on unmount (must be before any early returns)
  useEffect(() => {
    return () => {
      cleanupSubscriptions();
      if (smartRefreshIntervalRef.current) {
        clearInterval(smartRefreshIntervalRef.current);
      }
      if (sessionListRefreshRef.current) {
        clearInterval(sessionListRefreshRef.current);
      }
    };
  }, [cleanupSubscriptions]);

  const selectedSessionData = activeChatSessions.find(s => s.id === selectedSession);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading Dashboard...</p>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerLeft}>
          <div className={styles.logo}>
            <h1>Staff Dashboard</h1>
          </div>
        </div>

        <div className={styles.headerCenter}>
  <StaffStatus initialStatus={onlineStatus} onStatusChange={handleStatusChange} />
 {/* Realtime status and auto-refresh toggle */}
  <div style={{ marginLeft: '1rem', display: 'flex', alignItems: 'center', gap: '1rem' }}>
    <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
      {realtimeConnected ? '⚡ Real-time' : '🔄 Polling'}
    </div>
    <label style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', color: '#6b7280' }}>
      <input
        type="checkbox"
        checked={isAutoRefreshEnabled}
        onChange={(e) => setIsAutoRefreshEnabled(e.target.checked)}
        style={{ marginRight: '0.5rem' }}
      />
      Auto-refresh {isAutoRefreshEnabled ? '🟢' : '🔴'}
    </label>
  </div>
</div>

        <div className={styles.headerRight}>
  <NotificationCenter onNotificationClick={handleNotificationClick} />
  
  <div className={styles.userInfo}>
    <div className={styles.userAvatar}>
      {user?.display_name?.charAt(0) || 'S'}
    </div>
    <div className={styles.userDetails}>
      <span className={styles.userName}>{user?.display_name}</span>
      <span className={styles.userRole}>{user?.title || user?.role}</span>
    </div>
  </div>
  <button onClick={handleLogout} className={styles.logoutButton}>
    Logout
  </button>
</div>

      </header>

      {/* Main Content */}
      <div className={styles.mainContent}>
        {/* Sidebar - Chat Sessions */}
        <aside className={styles.sidebar}>
          <div className={styles.sidebarHeader}>
            <h2>Active Chats</h2>
            <span className={styles.chatCount}>{activeChatSessions.length}</span>
          </div>
	<ChatSearch
  onSearch={handleSearch}
  onFilter={handleFilter}
  totalChats={filteredChatSessions.length}
  activeFilters={chatFilters}
/>
		

          <div className={styles.chatList}>
  {filteredChatSessions.map((session) => (
   <div key={session.id} 
     className={`${styles.chatItem} ${selectedSession === session.id ? styles.selected : ''} ${session.unread_count > 0 ? styles.hasUnread : ''}`} 
     onClick={() => handleChatSelect(session.id)}
>
  {/* Add unread indicator */}
  {session.unread_count > 0 && (
    <div className={styles.unreadIndicator}>
      {session.unread_count}
    </div>
  )}
     
      <div className={styles.chatItemHeader}>
        <div className={styles.guestInfo}>
          <span className={styles.guestName}>{session.guest_name}</span>
          <span className={styles.roomInfo}>
            {session.room_number ? `Room ${session.room_number}` : session.source}
          </span>
        </div>
        <div className={styles.chatMeta}>
          <span 
            className={styles.priority}
            style={{ color: getPriorityColor(session.priority) }}
          >
            {session.priority.toUpperCase()}
          </span>
          <span className={styles.language}>{session.language}</span>
        </div>
      </div>

      <div className={styles.lastMessage}>
        {(() => {
          const messageDisplay = getSessionMessageDisplay(session);

          if (messageDisplay === null) {
            // Hiển thị loading spinner
            return (
              <div className={styles.loadingSpinner}>
                <span className={styles.spinner}></span>
                <span>Loading...</span>
              </div>
            );
          }

          return messageDisplay;
        })()}
      </div>

      <div className={styles.chatItemFooter}>
        <span className={styles.time}>{session.last_message_time}</span>
        <div className={styles.badges}>
          <span 
            className={styles.status}
            style={{ backgroundColor: getStatusColor(session.status) }}
          >
            {session.status}
          </span>
          {session.unread_count > 0 && (
            <span className={styles.unreadBadge}>
              {session.unread_count}
            </span>
          )}
        </div>
      </div>
    </div>
  ))}
  
  {filteredChatSessions.length === 0 && activeChatSessions.length > 0 && (
    <div className={styles.noResults}>
      <div className={styles.noResultsIcon}>🔍</div>
      <p>No chats match your search criteria</p>
      <button 
        className={styles.clearFiltersButton}
        onClick={() => {
          setSearchQuery('');
          setChatFilters({
            status: 'all',
            priority: 'all', 
            language: 'all',
            timeRange: 'all',
            unreadOnly: false
          });
        }}
      >
        Clear all filters
      </button>
    </div>
  )}
</div>
        </aside>

        {/* Chat Area */}
        <main className={styles.chatArea}>
          {selectedSession ? (
            <div className={styles.chatInterface}>
              <div className={styles.chatHeader}>
                <div className={styles.chatHeaderLeft}>
                  <h3>Chat with {selectedSessionData?.guest_name}</h3>
                  <div className={styles.chatHeaderMeta}>
                    <span className={styles.roomBadge}>
                      {selectedSessionData?.room_number 
                        ? `Room ${selectedSessionData.room_number}` 
                        : selectedSessionData?.source}
                    </span>
                    <span className={styles.languageBadge}>
                      {selectedSessionData?.language}
                    </span>
                    <span 
                      className={styles.priorityBadge}
                      style={{ color: getPriorityColor(selectedSessionData?.priority || 'normal') }}
                    >
                      {selectedSessionData?.priority?.toUpperCase()}
                    </span>
                  </div>
                </div>
                
                <div className={styles.chatActions}>
                  <button 
                    className={`${styles.translateButton} ${autoTranslate ? styles.active : ''}`}
                    onClick={handleToggleTranslation}
                  >
                    🌐 Auto Translate
                  </button>
                  <button className={styles.transferButton} onClick={handleTransferChat}>
                    📨 Transfer Chat
                  </button>
                  <button className={styles.endButton} onClick={handleEndChat}>
                    ❌ End Chat
                  </button>
                </div>
              </div>

              <div className={styles.chatMessages}>
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    {...message}
                  />
                ))}
                {isTyping && (
                  <div className={styles.typingIndicator}>
                    <div className={styles.typingDots}>
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <span>Guest is typing...</span>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              <ChatInput
                onSendMessage={handleSendMessage}
                autoTranslate={autoTranslate}
                onToggleTranslation={handleToggleTranslation}
                placeholder={`Message ${selectedSessionData?.guest_name}...`}
              />
            </div>
          ) : (
            <div className={styles.emptyChatArea}>
  <div className={styles.emptyState}>
    <ChatStats 
      activeSessions={activeChatSessions} 
      selectedSession={selectedSession} 
    />
    
    <div className={styles.emptyContent}>
      <div className={styles.emptyIcon}>💬</div>
      <h3>Select a Chat Session</h3>
      <p>Choose a conversation from the list to start helping guests</p>
    </div>
    
    <div className={styles.statsGrid}>
      <div className={styles.statCard}>
        <span className={styles.statNumber}>{activeChatSessions.length}</span>
        <span className={styles.statLabel}>Active Chats</span>
      </div>
      <div className={styles.statCard}>
        <span className={styles.statNumber}>
          {activeChatSessions.reduce((sum, session) => sum + session.unread_count, 0)}
        </span>
        <span className={styles.statLabel}>Unread Messages</span>
      </div>
      <div className={styles.statCard}>
        <span className={styles.statNumber}>
          {activeChatSessions.filter(s => s.priority === 'urgent' || s.priority === 'high').length}
        </span>
        <span className={styles.statLabel}>High Priority</span>
      </div>
    </div>

    <div className={styles.quickActions}>
      <h4>Quick Actions</h4>
      <div className={styles.actionButtons}>
        <button className={styles.actionButton}>
          <span className={styles.actionIcon}>📞</span>
          <span className={styles.actionText}>Make Call</span>
        </button>
        <button className={styles.actionButton}>
          <span className={styles.actionIcon}>📧</span>
          <span className={styles.actionText}>Send Email</span>
        </button>
        <button className={styles.actionButton}>
          <span className={styles.actionIcon}>📝</span>
          <span className={styles.actionText}>Create Note</span>
        </button>
        <button className={styles.actionButton}>
          <span className={styles.actionIcon}>📊</span>
          <span className={styles.actionText}>View Reports</span>
        </button>
      </div>
    </div>
  </div>
</div>

          )}
        </main>
      </div>
	<QuickActions onAction={handleQuickAction} />
    </div>
  );
}
