-- Script đơn giản để tạo QR codes cho testing multiple guests
-- Chạy script này trong Supabase SQL Editor

-- Lấy tenant_id hiện tại
WITH current_tenant AS (
    SELECT id as tenant_id FROM tenants WHERE is_active = true LIMIT 1
)

-- Tạo QR codes đơn giản
INSERT INTO tenant_qr_codes (
    tenant_id,
    code_value,
    name,
    location,
    description,
    room_number,
    target_type,
    target_department,
    is_active,
    status,
    scan_count,
    created_at,
    updated_at
)
SELECT 
    ct.tenant_id,
    qr_data.code_value,
    qr_data.name,
    qr_data.location,
    qr_data.description,
    qr_data.room_number,
    qr_data.target_type,
    qr_data.target_department,
    true,
    'active',
    0,
    now(),
    now()
FROM current_tenant ct
CROSS JOIN (
    VALUES 
        (
            'room102-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
            'Room 102 Guest Services',
            'Room 102 - Executive Suite',
            'QR Code for Room 102 guest services and support',
            '102',
            'room',
            'guest_services'
        ),
        (
            'restaurant-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
            'Restaurant Services',
            'Main Restaurant - Ground Floor',
            'QR Code for restaurant orders and services',
            null,
            'area',
            'food_beverage'
        ),
        (
            'spa-wellness-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
            'Spa & Wellness Center',
            'Spa Center - 2nd Floor',
            'QR Code for spa bookings and wellness services',
            null,
            'area',
            'spa_wellness'
        ),
        (
            'concierge-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
            'Concierge Services',
            'Main Lobby - Concierge Desk',
            'QR Code for concierge and local information services',
            null,
            'area',
            'concierge'
        ),
        (
            'housekeeping-test-' || FLOOR(EXTRACT(EPOCH FROM NOW()))::text,
            'Housekeeping Services',
            'Service Floor - Housekeeping Department',
            'QR Code for room cleaning and maintenance requests',
            null,
            'area',
            'housekeeping'
        )
) AS qr_data(code_value, name, location, description, room_number, target_type, target_department);

-- Hiển thị kết quả
SELECT 
    code_value,
    name,
    location,
    room_number,
    target_type,
    target_department,
    -- URLs test
    'http://localhost:3003/qr/' || code_value || '?lang=en' as test_url_en,
    'http://localhost:3003/qr/' || code_value || '?lang=vi' as test_url_vi,
    'http://localhost:3003/qr/' || code_value || '?lang=fr' as test_url_fr,
    'http://localhost:3003/qr/' || code_value || '?lang=ko' as test_url_ko,
    'http://localhost:3003/qr/' || code_value || '?lang=ja' as test_url_ja
FROM tenant_qr_codes 
WHERE created_at > now() - interval '1 minute'
  AND is_active = true
ORDER BY created_at DESC;

-- Thông báo
SELECT 
    '✅ QR codes created successfully!' as message,
    COUNT(*) as total_created
FROM tenant_qr_codes 
WHERE created_at > now() - interval '1 minute';

-- Hướng dẫn sử dụng
SELECT 
    'Copy the code_value from above results' as step_1,
    'Update test_multiple_guests.html with actual code_values' as step_2,
    'Open Staff Dashboard: http://localhost:3003/staff/dashboard' as step_3,
    'Open Guest URLs in separate tabs to test' as step_4;
