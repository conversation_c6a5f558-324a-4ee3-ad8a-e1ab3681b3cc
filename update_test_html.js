// Script để tự động cập nhật test_multiple_guests.html với QR codes thực tế
// Chạy script này sau khi đã tạo QR codes trong database

const fs = require('fs');
const path = require('path');

// QR codes mẫu - sẽ được thay thế bằng dữ liệu thực từ database
const sampleQrCodes = {
    room102: 'room102-test-1735123456',
    restaurant: 'restaurant-test-1735123457', 
    spa: 'spa-wellness-1735123458',
    concierge: 'concierge-test-1735123459',
    housekeeping: 'housekeeping-test-1735123460'
};

// Đọc file HTML hiện tại
const htmlFilePath = './test_multiple_guests.html';

try {
    let htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
    
    // Tìm và thay thế object qrCodes
    const qrCodesRegex = /const qrCodes = \{[\s\S]*?\};/;
    
    const newQrCodesObject = `const qrCodes = {
            room102: '${sampleQrCodes.room102}',
            restaurant: '${sampleQrCodes.restaurant}', 
            spa: '${sampleQrCodes.spa}',
            concierge: '${sampleQrCodes.concierge}',
            housekeeping: '${sampleQrCodes.housekeeping}'
        };`;
    
    htmlContent = htmlContent.replace(qrCodesRegex, newQrCodesObject);
    
    // Ghi lại file
    fs.writeFileSync(htmlFilePath, htmlContent, 'utf8');
    
    console.log('✅ Updated test_multiple_guests.html successfully!');
    console.log('📋 QR Codes updated:');
    Object.entries(sampleQrCodes).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
    });
    
} catch (error) {
    console.error('❌ Error updating HTML file:', error.message);
}

// Hướng dẫn sử dụng
console.log('\n📖 Instructions:');
console.log('1. Run create_test_qr_codes.sql in Supabase SQL Editor');
console.log('2. Copy the actual code_value results from the SQL output');
console.log('3. Update the sampleQrCodes object in this script with real values');
console.log('4. Run this script again: node update_test_html.js');
console.log('5. Open test_multiple_guests.html in browser to test');

// Export để có thể import và sử dụng
module.exports = {
    updateHtmlWithQrCodes: (qrCodes) => {
        try {
            let htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
            
            const qrCodesRegex = /const qrCodes = \{[\s\S]*?\};/;
            
            const newQrCodesObject = `const qrCodes = {
                room102: '${qrCodes.room102 || 'room102-test-placeholder'}',
                restaurant: '${qrCodes.restaurant || 'restaurant-test-placeholder'}', 
                spa: '${qrCodes.spa || 'spa-wellness-placeholder'}',
                concierge: '${qrCodes.concierge || 'concierge-test-placeholder'}',
                housekeeping: '${qrCodes.housekeeping || 'housekeeping-test-placeholder'}'
            };`;
            
            htmlContent = htmlContent.replace(qrCodesRegex, newQrCodesObject);
            fs.writeFileSync(htmlFilePath, htmlContent, 'utf8');
            
            return true;
        } catch (error) {
            console.error('Error updating HTML:', error);
            return false;
        }
    }
};
