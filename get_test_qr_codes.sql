-- <PERSON><PERSON>t để lấy thông tin QR codes để test
-- <PERSON><PERSON>y script này để lấy code_value cho testing

-- <PERSON><PERSON><PERSON> tất cả QR codes hiện có
SELECT 
    id,
    code_value,
    name,
    location,
    room_number,
    target_type,
    target_department,
    is_active,
    scan_count,
    created_at,
    -- Tạo URL test cho từng QR code
    'http://localhost:3003/qr/' || code_value || '?lang=en' as test_url_en,
    'http://localhost:3003/qr/' || code_value || '?lang=vi' as test_url_vi,
    'http://localhost:3003/qr/' || code_value || '?lang=fr' as test_url_fr
FROM tenant_qr_codes 
WHERE is_active = true
ORDER BY created_at DESC;

-- L<PERSON>y thông tin reception points
SELECT 
    id,
    name,
    code,
    description,
    is_active,
    priority
FROM tenant_message_reception_points 
WHERE is_active = true
ORDER BY priority;

-- L<PERSON>y thông tin tenant
SELECT 
    id,
    name,
    domain,
    created_at
FROM tenants 
ORDER BY created_at DESC;
