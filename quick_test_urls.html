<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test URLs - LoaLoa Multiple Guests</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .url-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .url-section h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
            border-left: 4px solid #f97316;
            padding-left: 15px;
        }
        .url-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .url-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #f8fafc;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        .url-item .emoji {
            font-size: 20px;
            width: 30px;
        }
        .url-item .url {
            flex: 1;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            color: #1e40af;
            word-break: break-all;
        }
        .url-item .copy-btn {
            padding: 5px 10px;
            background: #f97316;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .url-item .copy-btn:hover {
            background: #ea580c;
        }
        .url-item .open-btn {
            padding: 5px 10px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
        }
        .url-item .open-btn:hover {
            background: #059669;
        }
        .staff-dashboard {
            background: #fef3e2;
            border: 2px solid #f97316;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .staff-dashboard a {
            display: inline-block;
            padding: 15px 30px;
            background: #f97316;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
        }
        .instructions {
            background: #eff6ff;
            border: 1px solid #dbeafe;
            border-radius: 10px;
            padding: 20px;
            color: #1e40af;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            display: none;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="toast" id="toast">URL copied to clipboard!</div>

    <div class="header">
        <h1>🚀 Quick Test URLs - Multiple Guests</h1>
        <p>Copy và paste các URLs này để test multiple guest sessions</p>
    </div>

    <div class="staff-dashboard">
        <h3>👨‍💼 Staff Dashboard (Mở đầu tiên)</h3>
        <a href="http://localhost:3003/staff/dashboard" target="_blank">
            Open Staff Dashboard
        </a>
    </div>

    <div class="instructions">
        <h3>📋 Hướng dẫn:</h3>
        <ol>
            <li><strong>Mở Staff Dashboard</strong> trước (link ở trên)</li>
            <li><strong>Copy URLs</strong> dưới đây và mở trong tabs riêng biệt</li>
            <li><strong>Gửi tin nhắn</strong> từ mỗi guest để tạo sessions</li>
            <li><strong>Quan sát Staff Dashboard</strong> để test loading states và emoji notifications</li>
        </ol>
    </div>

    <!-- Guest 1: Room 101 (Existing) -->
    <div class="url-section">
        <h3>🏨 Guest 1 - Room 101 (Existing QR)</h3>
        <div class="url-list">
            <div class="url-item">
                <span class="emoji">🇺🇸</span>
                <span class="url">http://localhost:3003/qr/mbblsqjm-i24r2?lang=en</span>
                <button class="copy-btn" onclick="copyUrl('http://localhost:3003/qr/mbblsqjm-i24r2?lang=en')">Copy</button>
                <a class="open-btn" href="http://localhost:3003/qr/mbblsqjm-i24r2?lang=en" target="_blank">Open</a>
            </div>
            <div class="url-item">
                <span class="emoji">🇻🇳</span>
                <span class="url">http://localhost:3003/qr/mbblsqjm-i24r2?lang=vi</span>
                <button class="copy-btn" onclick="copyUrl('http://localhost:3003/qr/mbblsqjm-i24r2?lang=vi')">Copy</button>
                <a class="open-btn" href="http://localhost:3003/qr/mbblsqjm-i24r2?lang=vi" target="_blank">Open</a>
            </div>
        </div>
    </div>

    <!-- Guest 2: Room 102 -->
    <div class="url-section">
        <h3>🛏️ Guest 2 - Room 102 (New QR)</h3>
        <div class="url-list">
            <div class="url-item">
                <span class="emoji">🇺🇸</span>
                <span class="url" id="room102-en">http://localhost:3003/qr/[REPLACE_WITH_ROOM102_CODE]?lang=en</span>
                <button class="copy-btn" onclick="copyUrlById('room102-en')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('room102-en')" target="_blank">Open</a>
            </div>
            <div class="url-item">
                <span class="emoji">🇰🇷</span>
                <span class="url" id="room102-ko">http://localhost:3003/qr/[REPLACE_WITH_ROOM102_CODE]?lang=ko</span>
                <button class="copy-btn" onclick="copyUrlById('room102-ko')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('room102-ko')" target="_blank">Open</a>
            </div>
        </div>
    </div>

    <!-- Guest 3: Restaurant -->
    <div class="url-section">
        <h3>🍽️ Guest 3 - Restaurant</h3>
        <div class="url-list">
            <div class="url-item">
                <span class="emoji">🇺🇸</span>
                <span class="url" id="restaurant-en">http://localhost:3003/qr/[REPLACE_WITH_RESTAURANT_CODE]?lang=en</span>
                <button class="copy-btn" onclick="copyUrlById('restaurant-en')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('restaurant-en')" target="_blank">Open</a>
            </div>
            <div class="url-item">
                <span class="emoji">🇯🇵</span>
                <span class="url" id="restaurant-ja">http://localhost:3003/qr/[REPLACE_WITH_RESTAURANT_CODE]?lang=ja</span>
                <button class="copy-btn" onclick="copyUrlById('restaurant-ja')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('restaurant-ja')" target="_blank">Open</a>
            </div>
        </div>
    </div>

    <!-- Guest 4: Spa -->
    <div class="url-section">
        <h3>🧘 Guest 4 - Spa & Wellness</h3>
        <div class="url-list">
            <div class="url-item">
                <span class="emoji">🇫🇷</span>
                <span class="url" id="spa-fr">http://localhost:3003/qr/[REPLACE_WITH_SPA_CODE]?lang=fr</span>
                <button class="copy-btn" onclick="copyUrlById('spa-fr')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('spa-fr')" target="_blank">Open</a>
            </div>
            <div class="url-item">
                <span class="emoji">🇹🇭</span>
                <span class="url" id="spa-th">http://localhost:3003/qr/[REPLACE_WITH_SPA_CODE]?lang=th</span>
                <button class="copy-btn" onclick="copyUrlById('spa-th')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('spa-th')" target="_blank">Open</a>
            </div>
        </div>
    </div>

    <!-- Guest 5: Concierge -->
    <div class="url-section">
        <h3>🛎️ Guest 5 - Concierge</h3>
        <div class="url-list">
            <div class="url-item">
                <span class="emoji">🇨🇳</span>
                <span class="url" id="concierge-zh">http://localhost:3003/qr/[REPLACE_WITH_CONCIERGE_CODE]?lang=zh</span>
                <button class="copy-btn" onclick="copyUrlById('concierge-zh')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('concierge-zh')" target="_blank">Open</a>
            </div>
            <div class="url-item">
                <span class="emoji">🇩🇪</span>
                <span class="url" id="concierge-de">http://localhost:3003/qr/[REPLACE_WITH_CONCIERGE_CODE]?lang=de</span>
                <button class="copy-btn" onclick="copyUrlById('concierge-de')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('concierge-de')" target="_blank">Open</a>
            </div>
        </div>
    </div>

    <!-- Guest 6: Housekeeping -->
    <div class="url-section">
        <h3>🧹 Guest 6 - Housekeeping</h3>
        <div class="url-list">
            <div class="url-item">
                <span class="emoji">🇪🇸</span>
                <span class="url" id="housekeeping-es">http://localhost:3003/qr/[REPLACE_WITH_HOUSEKEEPING_CODE]?lang=es</span>
                <button class="copy-btn" onclick="copyUrlById('housekeeping-es')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('housekeeping-es')" target="_blank">Open</a>
            </div>
            <div class="url-item">
                <span class="emoji">🇮🇹</span>
                <span class="url" id="housekeeping-it">http://localhost:3003/qr/[REPLACE_WITH_HOUSEKEEPING_CODE]?lang=it</span>
                <button class="copy-btn" onclick="copyUrlById('housekeeping-it')">Copy</button>
                <a class="open-btn" href="#" onclick="openUrlById('housekeeping-it')" target="_blank">Open</a>
            </div>
        </div>
    </div>

    <script>
        function copyUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                showToast();
            });
        }

        function copyUrlById(elementId) {
            const element = document.getElementById(elementId);
            const url = element.textContent;
            navigator.clipboard.writeText(url).then(() => {
                showToast();
            });
        }

        function openUrlById(elementId) {
            const element = document.getElementById(elementId);
            const url = element.textContent;
            if (!url.includes('[REPLACE_WITH_')) {
                window.open(url, '_blank');
            } else {
                alert('Please replace the placeholder with actual QR code value first!');
            }
        }

        function showToast() {
            const toast = document.getElementById('toast');
            toast.style.display = 'block';
            setTimeout(() => {
                toast.style.display = 'none';
            }, 2000);
        }

        // Auto-update URLs when QR codes are available
        function updateUrls(qrCodes) {
            if (qrCodes.room102) {
                document.getElementById('room102-en').textContent = `http://localhost:3003/qr/${qrCodes.room102}?lang=en`;
                document.getElementById('room102-ko').textContent = `http://localhost:3003/qr/${qrCodes.room102}?lang=ko`;
            }
            if (qrCodes.restaurant) {
                document.getElementById('restaurant-en').textContent = `http://localhost:3003/qr/${qrCodes.restaurant}?lang=en`;
                document.getElementById('restaurant-ja').textContent = `http://localhost:3003/qr/${qrCodes.restaurant}?lang=ja`;
            }
            if (qrCodes.spa) {
                document.getElementById('spa-fr').textContent = `http://localhost:3003/qr/${qrCodes.spa}?lang=fr`;
                document.getElementById('spa-th').textContent = `http://localhost:3003/qr/${qrCodes.spa}?lang=th`;
            }
            if (qrCodes.concierge) {
                document.getElementById('concierge-zh').textContent = `http://localhost:3003/qr/${qrCodes.concierge}?lang=zh`;
                document.getElementById('concierge-de').textContent = `http://localhost:3003/qr/${qrCodes.concierge}?lang=de`;
            }
            if (qrCodes.housekeeping) {
                document.getElementById('housekeeping-es').textContent = `http://localhost:3003/qr/${qrCodes.housekeeping}?lang=es`;
                document.getElementById('housekeeping-it').textContent = `http://localhost:3003/qr/${qrCodes.housekeeping}?lang=it`;
            }
        }

        // Example usage - replace with actual QR codes from database
        // updateUrls({
        //     room102: 'room102-test-1735123456',
        //     restaurant: 'restaurant-test-1735123457',
        //     spa: 'spa-wellness-1735123458',
        //     concierge: 'concierge-test-1735123459',
        //     housekeeping: 'housekeeping-test-1735123460'
        // });

        console.log('🔧 To update URLs with actual QR codes:');
        console.log('1. Run create_test_qr_codes.sql in Supabase');
        console.log('2. Copy the code_value results');
        console.log('3. Call updateUrls() with the actual codes');
    </script>
</body>
</html>
