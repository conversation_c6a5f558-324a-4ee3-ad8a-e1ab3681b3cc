<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Multiple Guests - <PERSON><PERSON><PERSON><PERSON> Chat</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .guest-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .guest-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #f97316;
        }
        .guest-card h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .guest-card .emoji {
            font-size: 24px;
        }
        .test-links {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .test-link {
            display: inline-block;
            padding: 10px 15px;
            background: #f97316;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            transition: all 0.2s;
            font-weight: 500;
        }
        .test-link:hover {
            background: #ea580c;
            transform: translateY(-1px);
        }
        .test-link.secondary {
            background: #6b7280;
        }
        .test-link.secondary:hover {
            background: #4b5563;
        }
        .info {
            background: #eff6ff;
            border: 1px solid #dbeafe;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
            color: #1e40af;
        }
        .instructions {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .instructions h3 {
            color: #1f2937;
            margin-top: 0;
        }
        .instructions ol {
            color: #4b5563;
            line-height: 1.6;
        }
        .staff-dashboard {
            background: #fef3e2;
            border: 2px solid #f97316;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .staff-dashboard a {
            display: inline-block;
            padding: 15px 30px;
            background: #f97316;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s;
        }
        .staff-dashboard a:hover {
            background: #ea580c;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Test Multiple Guests - LoaLoa Chat System</h1>
        <p>Sử dụng các link dưới đây để mô phỏng nhiều guest chat cùng lúc với staff</p>
    </div>

    <div class="instructions">
        <h3>📋 Hướng dẫn test:</h3>
        <ol>
            <li><strong>Mở Staff Dashboard</strong> trước tiên (link ở cuối trang)</li>
            <li><strong>Mở từng Guest link</strong> trong tab/cửa sổ riêng biệt</li>
            <li><strong>Gửi tin nhắn</strong> từ mỗi guest để tạo session</li>
            <li><strong>Quan sát Staff Dashboard</strong> để thấy multiple sessions</li>
            <li><strong>Test các tính năng:</strong>
                <ul>
                    <li>Loading states khi chưa click vào session</li>
                    <li>Emoji 🔔 cho unread messages</li>
                    <li>Loading spinner khi đã click vào session</li>
                    <li>Multiple guest sessions cùng lúc</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="guest-grid">
        <!-- Guest 1: Room 101 (existing) -->
        <div class="guest-card">
            <h3><span class="emoji">🏨</span> Guest 1 - Room 101</h3>
            <div class="info">Existing QR code - Room 101 Deluxe Suite</div>
            <div class="test-links">
                <a href="http://localhost:3003/qr/mbblsqjm-i24r2?lang=en" class="test-link" target="_blank">
                    🇺🇸 Open as English Guest
                </a>
                <a href="http://localhost:3003/qr/mbblsqjm-i24r2?lang=vi" class="test-link secondary" target="_blank">
                    🇻🇳 Open as Vietnamese Guest
                </a>
                <a href="http://localhost:3003/qr/mbblsqjm-i24r2?lang=fr" class="test-link secondary" target="_blank">
                    🇫🇷 Open as French Guest
                </a>
            </div>
        </div>

        <!-- Guest 2: Room 102 -->
        <div class="guest-card">
            <h3><span class="emoji">🛏️</span> Guest 2 - Room 102</h3>
            <div class="info">New QR code - Room 102 Executive Suite</div>
            <div class="test-links">
                <a href="#" class="test-link" onclick="openRoom102('en')" target="_blank">
                    🇺🇸 Open as English Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openRoom102('vi')" target="_blank">
                    🇻🇳 Open as Vietnamese Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openRoom102('ko')" target="_blank">
                    🇰🇷 Open as Korean Guest
                </a>
            </div>
        </div>

        <!-- Guest 3: Restaurant -->
        <div class="guest-card">
            <h3><span class="emoji">🍽️</span> Guest 3 - Restaurant</h3>
            <div class="info">Restaurant services - Food & Beverage</div>
            <div class="test-links">
                <a href="#" class="test-link" onclick="openRestaurant('en')" target="_blank">
                    🇺🇸 Open as English Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openRestaurant('vi')" target="_blank">
                    🇻🇳 Open as Vietnamese Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openRestaurant('ja')" target="_blank">
                    🇯🇵 Open as Japanese Guest
                </a>
            </div>
        </div>

        <!-- Guest 4: Spa -->
        <div class="guest-card">
            <h3><span class="emoji">🧘</span> Guest 4 - Spa & Wellness</h3>
            <div class="info">Spa services - Wellness Center</div>
            <div class="test-links">
                <a href="#" class="test-link" onclick="openSpa('en')" target="_blank">
                    🇺🇸 Open as English Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openSpa('vi')" target="_blank">
                    🇻🇳 Open as Vietnamese Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openSpa('th')" target="_blank">
                    🇹🇭 Open as Thai Guest
                </a>
            </div>
        </div>

        <!-- Guest 5: Concierge -->
        <div class="guest-card">
            <h3><span class="emoji">🛎️</span> Guest 5 - Concierge</h3>
            <div class="info">Concierge services - Local information</div>
            <div class="test-links">
                <a href="#" class="test-link" onclick="openConcierge('en')" target="_blank">
                    🇺🇸 Open as English Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openConcierge('vi')" target="_blank">
                    🇻🇳 Open as Vietnamese Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openConcierge('zh')" target="_blank">
                    🇨🇳 Open as Chinese Guest
                </a>
            </div>
        </div>

        <!-- Guest 6: Housekeeping -->
        <div class="guest-card">
            <h3><span class="emoji">🧹</span> Guest 6 - Housekeeping</h3>
            <div class="info">Room cleaning and maintenance</div>
            <div class="test-links">
                <a href="#" class="test-link" onclick="openHousekeeping('en')" target="_blank">
                    🇺🇸 Open as English Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openHousekeeping('vi')" target="_blank">
                    🇻🇳 Open as Vietnamese Guest
                </a>
                <a href="#" class="test-link secondary" onclick="openHousekeeping('de')" target="_blank">
                    🇩🇪 Open as German Guest
                </a>
            </div>
        </div>
    </div>

    <div class="staff-dashboard">
        <h3>👨‍💼 Staff Dashboard</h3>
        <p>Mở Staff Dashboard để xem và quản lý tất cả guest sessions</p>
        <a href="http://localhost:3003/staff/dashboard" target="_blank">
            Open Staff Dashboard
        </a>
    </div>

    <script>
        // Placeholder QR codes - sẽ được thay thế bằng QR codes thực tế từ database
        const qrCodes = {
            room102: 'room102-test-placeholder',
            restaurant: 'restaurant-test-placeholder', 
            spa: 'spa-wellness-placeholder',
            concierge: 'concierge-test-placeholder',
            housekeeping: 'housekeeping-test-placeholder'
        };

        function openRoom102(lang) {
            window.open(`http://localhost:3003/qr/${qrCodes.room102}?lang=${lang}`, '_blank');
        }

        function openRestaurant(lang) {
            window.open(`http://localhost:3003/qr/${qrCodes.restaurant}?lang=${lang}`, '_blank');
        }

        function openSpa(lang) {
            window.open(`http://localhost:3003/qr/${qrCodes.spa}?lang=${lang}`, '_blank');
        }

        function openConcierge(lang) {
            window.open(`http://localhost:3003/qr/${qrCodes.concierge}?lang=${lang}`, '_blank');
        }

        function openHousekeeping(lang) {
            window.open(`http://localhost:3003/qr/${qrCodes.housekeeping}?lang=${lang}`, '_blank');
        }

        // Auto-update QR codes from database (placeholder)
        // Trong thực tế, bạn sẽ fetch từ API để lấy QR codes thực tế
        console.log('🔧 To update QR codes:');
        console.log('1. Run create_test_qr_codes.sql in Supabase');
        console.log('2. Run get_test_qr_codes.sql to get code_values');
        console.log('3. Update qrCodes object in this file');
    </script>
</body>
</html>
